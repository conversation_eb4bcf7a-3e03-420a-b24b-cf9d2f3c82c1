package com.quickspender.android.ui.screens.transactions

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.quickspender.android.data.model.CategoryType
import com.quickspender.android.data.model.TransactionType

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddTransactionScreen(
    onTransactionAdded: () -> Unit,
    viewModel: AddTransactionViewModel = hiltViewModel()
) {
    var amount by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var notes by remember { mutableStateOf("") }
    var selectedType by remember { mutableStateOf(TransactionType.EXPENSE) }
    var selectedCategoryId by remember { mutableStateOf<String?>(null) }
    
    val uiState by viewModel.uiState.collectAsState()
    
    LaunchedEffect(selectedType) {
        viewModel.loadCategories(selectedType.value)
    }
    
    LaunchedEffect(uiState.transactionAdded) {
        if (uiState.transactionAdded) {
            onTransactionAdded()
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        Text(
            text = "Add Transaction",
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.padding(bottom = 24.dp)
        )
        
        // Transaction Type Selection
        Text(
            text = "Type",
            style = MaterialTheme.typography.bodyLarge,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            TransactionType.entries.forEach { type ->
                Row(
                    modifier = Modifier
                        .selectable(
                            selected = selectedType == type,
                            onClick = { selectedType = type }
                        ),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = selectedType == type,
                        onClick = { selectedType = type }
                    )
                    Text(
                        text = if (type == TransactionType.EXPENSE) "Expense" else "Income",
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Amount Input
        OutlinedTextField(
            value = amount,
            onValueChange = { amount = it },
            label = { Text("Amount") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
            modifier = Modifier.fillMaxWidth(),
            singleLine = true,
            prefix = { Text("$") }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Description Input
        OutlinedTextField(
            value = description,
            onValueChange = { description = it },
            label = { Text("Description") },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Category Selection
        if (uiState.categories.isNotEmpty()) {
            var expanded by remember { mutableStateOf(false) }
            val selectedCategory = uiState.categories.find { it.id == selectedCategoryId }
            
            ExposedDropdownMenuBox(
                expanded = expanded,
                onExpandedChange = { expanded = !expanded }
            ) {
                OutlinedTextField(
                    value = selectedCategory?.name ?: "",
                    onValueChange = {},
                    readOnly = true,
                    label = { Text("Category") },
                    trailingIcon = { ExposedDropdownMenuDefaults.TrailingIcon(expanded = expanded) },
                    modifier = Modifier
                        .fillMaxWidth()
                        .menuAnchor()
                )
                
                ExposedDropdownMenu(
                    expanded = expanded,
                    onDismissRequest = { expanded = false }
                ) {
                    uiState.categories.forEach { category ->
                        DropdownMenuItem(
                            text = { Text(category.name) },
                            onClick = {
                                selectedCategoryId = category.id
                                expanded = false
                            }
                        )
                    }
                }
            }
        } else {
            // Show loading state for categories
            OutlinedTextField(
                value = "Loading categories...",
                onValueChange = {},
                readOnly = true,
                label = { Text("Category") },
                modifier = Modifier.fillMaxWidth(),
                enabled = false
            )

            // Dynamic category selection
            if (uiState.categories.isEmpty()) {
                if (uiState.isLoading) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                } else {
                    Text(
                        text = "No categories available. Please create categories first.",
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(8.dp)
                    )
                }
            } else {
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    items(uiState.categories) { category ->
                        FilterChip(
                            onClick = { selectedCategoryId = category.id },
                            label = {
                                Text(
                                    text = category.name,
                                    style = MaterialTheme.typography.bodySmall
                                )
                            },
                            selected = selectedCategoryId == category.id,
                            modifier = Modifier.padding(horizontal = 2.dp)
                        )
                    }
                }

                // Show selected category info
                selectedCategoryId?.let { categoryId ->
                    val selectedCategory = uiState.categories.find { it.id == categoryId }
                    selectedCategory?.let { category ->
                        Text(
                            text = "Selected: ${category.name}",
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.padding(top = 8.dp)
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))
        
        // Notes Input
        OutlinedTextField(
            value = notes,
            onValueChange = { notes = it },
            label = { Text("Notes (Optional)") },
            modifier = Modifier.fillMaxWidth(),
            maxLines = 3
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Simplified button logic for debugging
        val amountValid = amount.toDoubleOrNull() != null
        val descValid = description.isNotBlank()
        val categoryValid = selectedCategoryId != null
        val allValid = amountValid && descValid && categoryValid

        // Show individual validation status
        Text(
            text = "Validation: Amount=$amountValid, Desc=$descValid, Category=$categoryValid, All=$allValid",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(8.dp)
        )

        Text(
            text = "Loading: ${uiState.isLoading}, Button enabled: ${!uiState.isLoading && allValid}",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.padding(8.dp)
        )



        // Add Button - Simplified logic
        val buttonEnabled = remember(uiState.isLoading, allValid) {
            !uiState.isLoading && allValid
        }

        Button(
            onClick = {
                val amountValue = amount.toDoubleOrNull()
                if (amountValue != null && description.isNotBlank() && selectedCategoryId != null) {
                    viewModel.addTransaction(
                        amount = amountValue,
                        description = description,
                        notes = notes.ifBlank { null },
                        type = selectedType.value,
                        categoryId = selectedCategoryId!!
                    )
                }
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = buttonEnabled,
            colors = ButtonDefaults.buttonColors(
                containerColor = if (buttonEnabled) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            if (uiState.isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(20.dp),
                    color = MaterialTheme.colorScheme.onPrimary
                )
            } else {
                Text(
                    "Add Transaction",
                    color = if (buttonEnabled) MaterialTheme.colorScheme.onPrimary else MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        uiState.error?.let { error ->
            Spacer(modifier = Modifier.height(16.dp))
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.errorContainer)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = if (error.isBlank()) "Unknown error occurred" else error,
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Button(
                        onClick = { viewModel.clearError() },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.error
                        )
                    ) {
                        Text("Clear Error", color = MaterialTheme.colorScheme.onError)
                    }
                }
            }
        }
    }
}
