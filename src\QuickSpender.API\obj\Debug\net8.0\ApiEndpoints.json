[{"ContainingType": "QuickSpender.API.Controllers.AuthenticationController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Authentication/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "QuickSpender.Contracts.Authentication.LoginRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "QuickSpender.API.Controllers.AuthenticationController", "Method": "Register", "RelativePath": "api/Authentication/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "QuickSpender.Contracts.Authentication.RegisterRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "QuickSpender.API.Controllers.CategoriesController", "Method": "CreateCategory", "RelativePath": "api/Categories", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "QuickSpender.Contracts.Categories.CreateCategoryRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "QuickSpender.API.Controllers.CategoriesController", "Method": "GetCategories", "RelativePath": "api/Categories", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "type", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "QuickSpender.API.Controllers.ReportsController", "Method": "GetSummary", "RelativePath": "api/Reports/summary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fromDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "toDate", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "QuickSpender.API.Controllers.TransactionsController", "Method": "CreateTransaction", "RelativePath": "api/Transactions", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "QuickSpender.Contracts.Transactions.CreateTransactionRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "QuickSpender.API.Controllers.TransactionsController", "Method": "GetTransactions", "RelativePath": "api/Transactions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "toDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "categoryId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "type", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=8.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "QuickSpender.API.Controllers.TransactionsController", "Method": "DeleteTransaction", "RelativePath": "api/Transactions/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "QuickSpender.API.Controllers.HealthController", "Method": "Get", "RelativePath": "Health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}]