# QuickSpender Environment Configuration
# Copy this file to .env and update the values

# Environment
ASPNETCORE_ENVIRONMENT=Production

# API Configuration
API_PORT=8080

# Database Configuration (External PostgreSQL)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=quickspender
DB_USER=quickspender
DB_PASSWORD=QuickSpender123!
CONNECTION_STRING=Host=${DB_HOST};Port=${DB_PORT};Database=${DB_NAME};Username=${DB_USER};Password=${DB_PASSWORD}

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-that-is-at-least-32-characters-long
JWT_ISSUER=QuickSpender
JWT_AUDIENCE=QuickSpender
JWT_EXPIRY_MINUTES=60

# Domain Configuration
DOMAIN=your-domain.com
