# QuickSpender Environment Configuration
# Copy this file to .env and update the values

# Environment
ASPNETCORE_ENVIRONMENT=Production

# API Configuration
API_PORT=8080

# Database Configuration (External PostgreSQL)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=quickspender
DB_USER=quickspender
DB_PASSWORD=d9th0I1ajnXr2N2inE94zyzC796SceJcukq
CONNECTION_STRING=Host=host.docker.internal;Port=5432;Database=quickspender;Username=quickspender;Password=d9th0I1ajnXr2N2inE94zyzC796SceJcukq

# JWT Configuration
JWT_SECRET=f007d3f38e6dffe66ba58a41869f2276
JWT_ISSUER=QuickSpender
JWT_AUDIENCE=QuickSpender
JWT_EXPIRY_MINUTES=10080

# Domain Configuration
DOMAIN=your-domain.com
