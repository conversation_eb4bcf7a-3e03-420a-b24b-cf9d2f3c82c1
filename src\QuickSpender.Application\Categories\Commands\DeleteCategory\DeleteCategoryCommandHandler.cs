using MediatR;
using Microsoft.EntityFrameworkCore;
using QuickSpender.Application.Common.Interfaces;

namespace QuickSpender.Application.Categories.Commands.DeleteCategory;

public class DeleteCategoryCommandHandler : IRequestHandler<DeleteCategoryCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public DeleteCategoryCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public async Task Handle(DeleteCategoryCommand request, CancellationToken cancellationToken)
    {
        if (!_currentUserService.IsAuthenticated || _currentUserService.UserId is null)
        {
            throw new UnauthorizedAccessException("User is not authenticated.");
        }

        var category = await _context.Categories
            .FirstOrDefaultAsync(c => c.Id == request.Id && c.UserId == _currentUserService.UserId, cancellationToken);

        if (category is null)
        {
            throw new KeyNotFoundException("Category not found or you don't have permission to delete it.");
        }

        // Check if category is being used by any transactions
        var hasTransactions = await _context.Transactions
            .AnyAsync(t => t.CategoryId == request.Id, cancellationToken);

        if (hasTransactions)
        {
            throw new InvalidOperationException("Cannot delete category that is being used by transactions.");
        }

        _context.Categories.Remove(category);
        await _context.SaveChangesAsync(cancellationToken);
    }
}
