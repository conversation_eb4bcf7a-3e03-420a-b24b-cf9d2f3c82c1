using MediatR;
using Microsoft.EntityFrameworkCore;
using QuickSpender.Application.Common.Interfaces;

namespace QuickSpender.Application.Categories.Commands.DeleteCategory;

public class DeleteCategoryCommandHandler : IRequestHandler<DeleteCategoryCommand>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;

    public DeleteCategoryCommandHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService)
    {
        _context = context;
        _currentUserService = currentUserService;
    }

    public async Task Handle(DeleteCategoryCommand request, CancellationToken cancellationToken)
    {
        if (!_currentUserService.IsAuthenticated || _currentUserService.UserId is null)
        {
            Console.WriteLine($"DeleteCategory: User not authenticated. IsAuthenticated: {_currentUserService.IsAuthenticated}, UserId: {_currentUserService.UserId}");
            throw new UnauthorizedAccessException("User is not authenticated.");
        }

        Console.WriteLine($"DeleteCategory: Attempting to delete category {request.Id} for user {_currentUserService.UserId}");

        // First, check if category exists at all
        var categoryExists = await _context.Categories
            .FirstOrDefaultAsync(c => c.Id == request.Id, cancellationToken);

        if (categoryExists is null)
        {
            Console.WriteLine($"DeleteCategory: Category {request.Id} does not exist in database");
            throw new KeyNotFoundException("Category not found.");
        }

        Console.WriteLine($"DeleteCategory: Category found - Name: {categoryExists.Name}, UserId: {categoryExists.UserId}, IsDefault: {categoryExists.IsDefault}");

        // Then check if it belongs to the current user
        var category = await _context.Categories
            .FirstOrDefaultAsync(c => c.Id == request.Id && c.UserId == _currentUserService.UserId, cancellationToken);

        if (category is null)
        {
            Console.WriteLine($"DeleteCategory: Category {request.Id} does not belong to user {_currentUserService.UserId}");
            throw new KeyNotFoundException("Category not found or you don't have permission to delete it.");
        }

        // Find all transactions using this category
        var transactionsToDelete = await _context.Transactions
            .Where(t => t.CategoryId == request.Id)
            .ToListAsync(cancellationToken);

        if (transactionsToDelete.Any())
        {
            Console.WriteLine($"DeleteCategory: Found {transactionsToDelete.Count} transactions to delete with category '{category.Name}'");

            // Remove all transactions that use this category
            _context.Transactions.RemoveRange(transactionsToDelete);
        }

        // Remove the category
        _context.Categories.Remove(category);

        Console.WriteLine($"DeleteCategory: Deleting category '{category.Name}' and {transactionsToDelete.Count} associated transactions");
        await _context.SaveChangesAsync(cancellationToken);
    }
}
