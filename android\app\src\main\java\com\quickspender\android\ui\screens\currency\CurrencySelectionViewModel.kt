package com.quickspender.android.ui.screens.currency

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.quickspender.android.data.model.Currency
import com.quickspender.android.data.repository.CurrencyRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class CurrencySelectionViewModel @Inject constructor(
    private val currencyRepository: CurrencyRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(CurrencySelectionUiState())
    val uiState: StateFlow<CurrencySelectionUiState> = _uiState.asStateFlow()
    
    fun loadCurrencies() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                val currencies = currencyRepository.getAllCurrencies()
                val currentCurrency = currencyRepository.getCurrentCurrencySync()
                
                _uiState.value = _uiState.value.copy(
                    currencies = currencies,
                    currentCurrency = currentCurrency,
                    isLoading = false
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to load currencies: ${e.message}"
                )
            }
        }
    }
    
    fun selectCurrency(currency: Currency) {
        viewModelScope.launch {
            try {
                currencyRepository.setCurrency(currency)
                _uiState.value = _uiState.value.copy(
                    currentCurrency = currency,
                    successMessage = "Currency changed to ${currency.code}",
                    error = null
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to save currency: ${e.message}"
                )
            }
        }
    }
    
    fun clearMessages() {
        _uiState.value = _uiState.value.copy(
            successMessage = null,
            error = null
        )
    }
}

data class CurrencySelectionUiState(
    val isLoading: Boolean = false,
    val currencies: List<Currency> = emptyList(),
    val currentCurrency: Currency = Currency.USD,
    val successMessage: String? = null,
    val error: String? = null
)
