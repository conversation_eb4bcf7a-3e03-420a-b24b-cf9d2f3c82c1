/ Header Record For PersistentHashMapValueStorage; :app/src/main/java/com/quickspender/android/MainActivity.ktF Eapp/src/main/java/com/quickspender/android/QuickSpenderApplication.kt? >app/src/main/java/com/quickspender/android/data/api/AuthApi.ktC Bapp/src/main/java/com/quickspender/android/data/api/CategoryApi.ktA @app/src/main/java/com/quickspender/android/data/api/ReportApi.ktF Eapp/src/main/java/com/quickspender/android/data/api/TransactionApi.ktN Mapp/src/main/java/com/quickspender/android/data/local/QuickSpenderDatabase.ktI Happ/src/main/java/com/quickspender/android/data/local/dao/CategoryDao.ktL Kapp/src/main/java/com/quickspender/android/data/local/dao/TransactionDao.ktB Aapp/src/main/java/com/quickspender/android/data/model/Category.kt@ ?app/src/main/java/com/quickspender/android/data/model/Report.ktE Dapp/src/main/java/com/quickspender/android/data/model/Transaction.kt> =app/src/main/java/com/quickspender/android/data/model/User.ktO Napp/src/main/java/com/quickspender/android/data/preferences/UserPreferences.ktM Lapp/src/main/java/com/quickspender/android/data/repository/AuthRepository.ktQ Papp/src/main/java/com/quickspender/android/data/repository/CategoryRepository.ktT Sapp/src/main/java/com/quickspender/android/data/repository/TransactionRepository.kt@ ?app/src/main/java/com/quickspender/android/di/DatabaseModule.kt? >app/src/main/java/com/quickspender/android/di/NetworkModule.ktV Uapp/src/main/java/com/quickspender/android/ui/components/PasswordStrengthIndicator.ktS Rapp/src/main/java/com/quickspender/android/ui/navigation/QuickSpenderNavigation.ktL Kapp/src/main/java/com/quickspender/android/ui/screens/auth/AuthViewModel.ktJ Iapp/src/main/java/com/quickspender/android/ui/screens/auth/LoginScreen.ktM Lapp/src/main/java/com/quickspender/android/ui/screens/auth/RegisterScreen.ktS Rapp/src/main/java/com/quickspender/android/ui/screens/dashboard/DashboardScreen.ktV Uapp/src/main/java/com/quickspender/android/ui/screens/dashboard/DashboardViewModel.ktS Rapp/src/main/java/com/quickspender/android/ui/screens/dashboard/TransactionItem.ktI Happ/src/main/java/com/quickspender/android/ui/screens/main/MainScreen.ktO Napp/src/main/java/com/quickspender/android/ui/screens/profile/ProfileScreen.kt[ Zapp/src/main/java/com/quickspender/android/ui/screens/transactions/AddTransactionScreen.kt^ ]app/src/main/java/com/quickspender/android/ui/screens/transactions/AddTransactionViewModel.kt\ [app/src/main/java/com/quickspender/android/ui/screens/transactions/TransactionListScreen.kt_ ^app/src/main/java/com/quickspender/android/ui/screens/transactions/TransactionListViewModel.kt= <app/src/main/java/com/quickspender/android/ui/theme/Color.kt= <app/src/main/java/com/quickspender/android/ui/theme/Theme.kt< ;app/src/main/java/com/quickspender/android/ui/theme/Type.ktF Eapp/src/main/java/com/quickspender/android/utils/PasswordValidator.kt[ Zapp/src/main/java/com/quickspender/android/ui/screens/transactions/AddTransactionScreen.ktQ Papp/src/main/java/com/quickspender/android/data/repository/CategoryRepository.ktQ Papp/src/main/java/com/quickspender/android/data/repository/CategoryRepository.kt? >app/src/main/java/com/quickspender/android/di/NetworkModule.ktM Lapp/src/main/java/com/quickspender/android/ui/screens/auth/RegisterScreen.kt[ Zapp/src/main/java/com/quickspender/android/ui/screens/transactions/AddTransactionScreen.kt[ Zapp/src/main/java/com/quickspender/android/ui/screens/transactions/AddTransactionScreen.kt[ Zapp/src/main/java/com/quickspender/android/ui/screens/transactions/AddTransactionScreen.kt[ Zapp/src/main/java/com/quickspender/android/ui/screens/transactions/AddTransactionScreen.kt^ ]app/src/main/java/com/quickspender/android/ui/screens/transactions/AddTransactionViewModel.kt[ Zapp/src/main/java/com/quickspender/android/ui/screens/transactions/AddTransactionScreen.kt^ ]app/src/main/java/com/quickspender/android/ui/screens/transactions/AddTransactionViewModel.kt[ Zapp/src/main/java/com/quickspender/android/ui/screens/transactions/AddTransactionScreen.kt[ Zapp/src/main/java/com/quickspender/android/ui/screens/transactions/AddTransactionScreen.kt[ Zapp/src/main/java/com/quickspender/android/ui/screens/transactions/AddTransactionScreen.kt? >app/src/main/java/com/quickspender/android/data/api/AuthApi.ktC Bapp/src/main/java/com/quickspender/android/data/api/CategoryApi.ktF Eapp/src/main/java/com/quickspender/android/data/api/TransactionApi.kt? >app/src/main/java/com/quickspender/android/data/api/AuthApi.ktC Bapp/src/main/java/com/quickspender/android/data/api/CategoryApi.ktA @app/src/main/java/com/quickspender/android/data/api/ReportApi.ktF Eapp/src/main/java/com/quickspender/android/data/api/TransactionApi.kt? >app/src/main/java/com/quickspender/android/di/NetworkModule.ktT Sapp/src/main/java/com/quickspender/android/data/repository/TransactionRepository.ktV Uapp/src/main/java/com/quickspender/android/ui/screens/dashboard/DashboardViewModel.ktV Uapp/src/main/java/com/quickspender/android/ui/screens/dashboard/DashboardViewModel.ktL Kapp/src/main/java/com/quickspender/android/data/local/dao/TransactionDao.ktT Sapp/src/main/java/com/quickspender/android/data/repository/TransactionRepository.ktS Rapp/src/main/java/com/quickspender/android/ui/screens/dashboard/TransactionItem.kt\ [app/src/main/java/com/quickspender/android/ui/screens/transactions/TransactionListScreen.kt_ ^app/src/main/java/com/quickspender/android/ui/screens/transactions/TransactionListViewModel.ktS Rapp/src/main/java/com/quickspender/android/ui/screens/dashboard/DashboardScreen.ktT Sapp/src/main/java/com/quickspender/android/data/repository/TransactionRepository.kt? >app/src/main/java/com/quickspender/android/di/NetworkModule.ktV Uapp/src/main/java/com/quickspender/android/ui/screens/dashboard/DashboardViewModel.kt[ Zapp/src/main/java/com/quickspender/android/ui/screens/transactions/AddTransactionScreen.kt^ ]app/src/main/java/com/quickspender/android/ui/screens/transactions/AddTransactionViewModel.ktO Napp/src/main/java/com/quickspender/android/data/preferences/UserPreferences.ktS Rapp/src/main/java/com/quickspender/android/ui/navigation/QuickSpenderNavigation.ktL Kapp/src/main/java/com/quickspender/android/ui/screens/auth/AuthViewModel.ktJ Iapp/src/main/java/com/quickspender/android/ui/screens/auth/LoginScreen.kt? >app/src/main/java/com/quickspender/android/di/NetworkModule.ktJ Iapp/src/main/java/com/quickspender/android/ui/screens/auth/LoginScreen.ktJ Iapp/src/main/java/com/quickspender/android/ui/screens/auth/LoginScreen.ktM Lapp/src/main/java/com/quickspender/android/data/repository/AuthRepository.ktJ Iapp/src/main/java/com/quickspender/android/ui/screens/auth/LoginScreen.ktM Lapp/src/main/java/com/quickspender/android/data/repository/AuthRepository.ktI Happ/src/main/java/com/quickspender/android/data/local/dao/CategoryDao.ktQ Papp/src/main/java/com/quickspender/android/data/repository/CategoryRepository.ktS Rapp/src/main/java/com/quickspender/android/ui/navigation/QuickSpenderNavigation.kt] \app/src/main/java/com/quickspender/android/ui/screens/categories/CategoryManagementScreen.kt` _app/src/main/java/com/quickspender/android/ui/screens/categories/CategoryManagementViewModel.ktI Happ/src/main/java/com/quickspender/android/ui/screens/main/MainScreen.ktO Napp/src/main/java/com/quickspender/android/ui/screens/profile/ProfileScreen.kt[ Zapp/src/main/java/com/quickspender/android/ui/screens/transactions/AddTransactionScreen.kt[ Zapp/src/main/java/com/quickspender/android/ui/screens/transactions/AddTransactionScreen.ktO Napp/src/main/java/com/quickspender/android/data/repository/ReportRepository.ktT Sapp/src/main/java/com/quickspender/android/ui/components/charts/CategoryBarChart.ktT Sapp/src/main/java/com/quickspender/android/ui/components/charts/CategoryPieChart.ktS Rapp/src/main/java/com/quickspender/android/ui/screens/dashboard/DashboardScreen.ktV Uapp/src/main/java/com/quickspender/android/ui/screens/dashboard/DashboardViewModel.ktS Rapp/src/main/java/com/quickspender/android/ui/screens/dashboard/DashboardScreen.ktV Uapp/src/main/java/com/quickspender/android/ui/screens/dashboard/DashboardViewModel.ktI Happ/src/main/java/com/quickspender/android/ui/screens/main/MainScreen.kt