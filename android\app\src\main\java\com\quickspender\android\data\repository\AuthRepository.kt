package com.quickspender.android.data.repository

import com.quickspender.android.data.api.AuthApi
import com.quickspender.android.data.model.AuthenticationResponse
import com.quickspender.android.data.model.LoginRequest
import com.quickspender.android.data.model.RegisterRequest
import com.quickspender.android.data.preferences.UserPreferences
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class AuthRepository @Inject constructor(
    private val authApi: AuthApi,
    private val userPreferences: UserPreferences
) {
    val isLoggedIn: Flow<Boolean> = userPreferences.isLoggedIn
    val authToken: Flow<String?> = userPreferences.authToken
    val currentUser: Flow<AuthenticationResponse?> = userPreferences.currentUser
    
    suspend fun login(email: String, password: String): Result<AuthenticationResponse> {
        return try {
            val response = authApi.login(LoginRequest(email, password))
            if (response.isSuccessful && response.body() != null) {
                val authResponse = response.body()!!
                userPreferences.saveAuthData(authResponse)
                Result.success(authResponse)
            } else {
                println("AuthRepository: Login failed with status code: ${response.code()}")
                println("AuthRepository: Response message: ${response.message()}")

                // Try to parse error message from response body
                val errorBody = response.errorBody()?.string()
                println("AuthRepository: Response body: $errorBody")

                val errorMessage = when (response.code()) {
                    401 -> "Invalid email or password"
                    400 -> "Invalid login data"
                    404 -> "Account not found"
                    409 -> "Account conflict"
                    500 -> "Server error"
                    else -> "Unable to login. Please try again."
                }
                Result.failure(Exception(errorMessage))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun register(
        firstName: String,
        lastName: String,
        email: String,
        password: String
    ): Result<AuthenticationResponse> {
        return try {
            val response = authApi.register(
                RegisterRequest(firstName, lastName, email, password)
            )
            if (response.isSuccessful && response.body() != null) {
                val authResponse = response.body()!!
                userPreferences.saveAuthData(authResponse)
                Result.success(authResponse)
            } else {
                val errorMessage = when (response.code()) {
                    400 -> "Invalid registration data"
                    409 -> "Email already exists"
                    500 -> "Server error"
                    else -> "Unable to create account. Please try again."
                }
                Result.failure(Exception(errorMessage))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun logout() {
        userPreferences.clearAuthData()
    }
}
