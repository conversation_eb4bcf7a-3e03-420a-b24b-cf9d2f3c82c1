package com.quickspender.android.data.preferences

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.google.gson.Gson
import com.quickspender.android.data.model.AuthenticationResponse
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "user_preferences")

@Singleton
class UserPreferences @Inject constructor(
    @ApplicationContext private val context: Context,
    private val gson: Gson
) {
    private object PreferencesKeys {
        val IS_LOGGED_IN = booleanPreferencesKey("is_logged_in")
        val AUTH_TOKEN = stringPreferencesKey("auth_token")
        val CURRENT_USER = stringPreferencesKey("current_user")
    }
    
    val isLoggedIn: Flow<Boolean> = context.dataStore.data.map { preferences ->
        preferences[PreferencesKeys.IS_LOGGED_IN] ?: false
    }
    
    val authToken: Flow<String?> = context.dataStore.data.map { preferences ->
        preferences[PreferencesKeys.AUTH_TOKEN]
    }
    
    val currentUser: Flow<AuthenticationResponse?> = context.dataStore.data.map { preferences ->
        preferences[PreferencesKeys.CURRENT_USER]?.let { userJson ->
            try {
                gson.fromJson(userJson, AuthenticationResponse::class.java)
            } catch (e: Exception) {
                null
            }
        }
    }
    
    suspend fun saveAuthData(authResponse: AuthenticationResponse) {
        println("UserPreferences: Saving auth data for user: ${authResponse.email}")
        context.dataStore.edit { preferences ->
            preferences[PreferencesKeys.IS_LOGGED_IN] = true
            preferences[PreferencesKeys.AUTH_TOKEN] = authResponse.token
            preferences[PreferencesKeys.CURRENT_USER] = gson.toJson(authResponse)
        }
        println("UserPreferences: Auth data saved successfully")
    }
    
    suspend fun clearAuthData() {
        context.dataStore.edit { preferences ->
            preferences.clear()
        }
    }
}
