package com.quickspender.android.data.repository

import com.quickspender.android.data.api.CategoryApi
import com.quickspender.android.data.local.dao.CategoryDao
import com.quickspender.android.data.model.Category
import com.quickspender.android.data.model.CreateCategoryRequest
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CategoryRepository @Inject constructor(
    private val categoryApi: CategoryApi,
    private val categoryDao: CategoryDao
) {
    fun getLocalCategories(): Flow<List<Category>> {
        return categoryDao.getAllCategories()
    }
    
    fun getLocalCategoriesByType(type: Int): Flow<List<Category>> {
        return categoryDao.getCategoriesByType(type)
    }
    
    suspend fun getCategories(type: Int? = null): List<Category> {
        return try {
            val response = categoryApi.getCategories(type)
            if (response.isSuccessful && response.body() != null) {
                val categories = response.body()!!
                // Cache categories locally
                categoryDao.insertCategories(categories)
                categories
            } else {
                throw Exception("Failed to fetch categories: ${response.message()}")
            }
        } catch (e: Exception) {
            // Return cached data if network fails
            emptyList() // For now, return empty list on error
        }
    }
    
    suspend fun createCategory(
        name: String,
        description: String?,
        color: String,
        icon: String,
        type: Int
    ): Result<Category> {
        return try {
            val request = CreateCategoryRequest(
                name = name,
                description = description,
                color = color,
                icon = icon,
                type = type
            )
            
            val response = categoryApi.createCategory(request)
            if (response.isSuccessful && response.body() != null) {
                val category = response.body()!!
                
                // Cache locally
                categoryDao.insertCategory(category)
                
                Result.success(category)
            } else {
                Result.failure(Exception("Failed to create category: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun deleteCategory(categoryId: String): Result<Unit> {
        return try {
            val response = categoryApi.deleteCategory(categoryId)
            if (response.isSuccessful) {
                // Remove from local cache
                categoryDao.deleteCategoryById(categoryId)
                Result.success(Unit)
            } else {
                Result.failure(Exception("Failed to delete category: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
