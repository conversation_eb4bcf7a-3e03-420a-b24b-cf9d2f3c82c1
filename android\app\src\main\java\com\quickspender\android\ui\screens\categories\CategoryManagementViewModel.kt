package com.quickspender.android.ui.screens.categories

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.quickspender.android.data.model.Category
import com.quickspender.android.data.repository.CategoryRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

data class CategoryManagementUiState(
    val categories: List<Category> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null
)

@HiltViewModel
class CategoryManagementViewModel @Inject constructor(
    private val categoryRepository: CategoryRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(CategoryManagementUiState())
    val uiState: StateFlow<CategoryManagementUiState> = _uiState.asStateFlow()

    fun loadCategories() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            try {
                // Load both expense and income categories
                val expenseCategories = categoryRepository.getCategories(0) // Expense
                val incomeCategories = categoryRepository.getCategories(1)  // Income
                val allCategories = expenseCategories + incomeCategories

                println("CategoryManagement: Loaded ${allCategories.size} categories")
                allCategories.forEach { category ->
                    println("CategoryManagement: Category - ID: ${category.id}, Name: ${category.name}, IsDefault: ${category.isDefault}")
                }

                _uiState.value = _uiState.value.copy(
                    categories = allCategories,
                    isLoading = false
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to load categories: ${e.message}"
                )
            }
        }
    }

    fun createCategory(
        name: String,
        description: String?,
        color: String,
        icon: String,
        type: Int
    ) {
        viewModelScope.launch {
            try {
                val result = categoryRepository.createCategory(
                    name = name,
                    description = description,
                    color = color,
                    icon = icon,
                    type = type
                )
                
                result.onSuccess {
                    // Reload categories after successful creation
                    loadCategories()
                }.onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        error = "Failed to create category: ${error.message}"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to create category: ${e.message}"
                )
            }
        }
    }

    fun deleteCategory(categoryId: String) {
        viewModelScope.launch {
            try {
                println("CategoryManagement: Attempting to delete category: $categoryId")
                val result = categoryRepository.deleteCategory(categoryId)
                
                result.onSuccess {
                    // Reload categories after successful deletion
                    loadCategories()
                }.onFailure { error ->
                    val errorMessage = when {
                        error.message?.contains("being used by") == true -> error.message
                        error.message?.contains("not found") == true -> "Category not found or already deleted"
                        else -> "Failed to delete category: ${error.message}"
                    }
                    _uiState.value = _uiState.value.copy(error = errorMessage)
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = "Failed to delete category: ${e.message}"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}
