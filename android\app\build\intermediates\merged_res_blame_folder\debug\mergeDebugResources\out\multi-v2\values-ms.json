{"logs": [{"outputFile": "com.quickspender.android.app-mergeDebugResources-68:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\668b07b585425ac50b3664ae01d10c25\\transformed\\foundation-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,90", "endOffsets": "137,228"}, "to": {"startLines": "113,114", "startColumns": "4,4", "startOffsets": "11563,11650", "endColumns": "86,90", "endOffsets": "11645,11736"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7e5c272ed8753b7bca75d20a7afd60fc\\transformed\\ui-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,279,375,477,562,645,740,827,912,997,1083,1155,1232,1305,1378,1454,1520", "endColumns": "89,83,95,101,84,82,94,86,84,84,85,71,76,72,72,75,65,119", "endOffsets": "190,274,370,472,557,640,735,822,907,992,1078,1150,1227,1300,1373,1449,1515,1635"}, "to": {"startLines": "36,37,38,39,40,41,42,100,101,102,103,105,106,107,108,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3543,3633,3717,3813,3915,4000,4083,10481,10568,10653,10738,10905,10977,11054,11127,11301,11377,11443", "endColumns": "89,83,95,101,84,82,94,86,84,84,85,71,76,72,72,75,65,119", "endOffsets": "3628,3712,3808,3910,3995,4078,4173,10563,10648,10733,10819,10972,11049,11122,11195,11372,11438,11558"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f011758bbd5e1f3717f8904866ac8535\\transformed\\appcompat-1.7.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,10824", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,10900"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ffc69b87a74cd983f3333cb07d0c197f\\transformed\\core-1.15.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "29,30,31,32,33,34,35,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2800,2895,2997,3094,3204,3310,3428,11200", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "2890,2992,3089,3199,3305,3423,3538,11296"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e6ee2619a776087357431d65cec3fd3a\\transformed\\material3-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,294,408,527,627,732,854,1004,1132,1280,1366,1466,1558,1656,1772,1898,2003,2141,2276,2408,2587,2712,2837,2965,3094,3187,3288,3409,3537,3638,3745,3851,3992,4138,4245,4344,4420,4518,4616,4718,4805,4894,4996,5076,5159,5258,5357,5454,5557,5644,5747,5846,5953,6075,6156,6262", "endColumns": "119,118,113,118,99,104,121,149,127,147,85,99,91,97,115,125,104,137,134,131,178,124,124,127,128,92,100,120,127,100,106,105,140,145,106,98,75,97,97,101,86,88,101,79,82,98,98,96,102,86,102,98,106,121,80,105,95", "endOffsets": "170,289,403,522,622,727,849,999,1127,1275,1361,1461,1553,1651,1767,1893,1998,2136,2271,2403,2582,2707,2832,2960,3089,3182,3283,3404,3532,3633,3740,3846,3987,4133,4240,4339,4415,4513,4611,4713,4800,4889,4991,5071,5154,5253,5352,5449,5552,5639,5742,5841,5948,6070,6151,6257,6353"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4178,4298,4417,4531,4650,4750,4855,4977,5127,5255,5403,5489,5589,5681,5779,5895,6021,6126,6264,6399,6531,6710,6835,6960,7088,7217,7310,7411,7532,7660,7761,7868,7974,8115,8261,8368,8467,8543,8641,8739,8841,8928,9017,9119,9199,9282,9381,9480,9577,9680,9767,9870,9969,10076,10198,10279,10385", "endColumns": "119,118,113,118,99,104,121,149,127,147,85,99,91,97,115,125,104,137,134,131,178,124,124,127,128,92,100,120,127,100,106,105,140,145,106,98,75,97,97,101,86,88,101,79,82,98,98,96,102,86,102,98,106,121,80,105,95", "endOffsets": "4293,4412,4526,4645,4745,4850,4972,5122,5250,5398,5484,5584,5676,5774,5890,6016,6121,6259,6394,6526,6705,6830,6955,7083,7212,7305,7406,7527,7655,7756,7863,7969,8110,8256,8363,8462,8538,8636,8734,8836,8923,9012,9114,9194,9277,9376,9475,9572,9675,9762,9865,9964,10071,10193,10274,10380,10476"}}]}]}