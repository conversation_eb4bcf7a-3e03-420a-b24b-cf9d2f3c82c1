package com.quickspender.android.data.model

enum class Currency(
    val code: String,
    val symbol: String,
    val displayName: String
) {
    USD("USD", "$", "US Dollar"),
    EUR("EUR", "€", "Euro"),
    PLN("PLN", "zł", "Polish Złoty"),
    GBP("GBP", "£", "British Pound"),
    JPY("JPY", "¥", "Japanese Yen"),
    CAD("CAD", "C$", "Canadian Dollar"),
    AUD("AUD", "A$", "Australian Dollar"),
    CHF("CHF", "CHF", "Swiss Franc"),
    CNY("CNY", "¥", "Chinese Yuan"),
    SEK("SEK", "kr", "Swedish Krona"),
    NOK("NOK", "kr", "Norwegian Krone"),
    DKK("DKK", "kr", "Danish Krone"),
    CZK("CZK", "Kč", "Czech Koruna"),
    HUF("HUF", "Ft", "Hungarian Forint"),
    RUB("RUB", "₽", "Russian Ruble"),
    BRL("BRL", "R$", "Brazilian Real"),
    INR("INR", "₹", "Indian Rupee"),
    KRW("KRW", "₩", "South Korean Won"),
    SGD("SGD", "S$", "Singapore Dollar"),
    HKD("HKD", "HK$", "Hong Kong Dollar");

    companion object {
        fun fromCode(code: String): Currency {
            return values().find { it.code == code } ?: USD
        }
        
        fun getDisplayText(currency: Currency): String {
            return "${currency.symbol} ${currency.code} - ${currency.displayName}"
        }
    }
}

data class CurrencyPreference(
    val currency: Currency = Currency.USD
)
