package com.quickspender.android.ui.navigation

import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.quickspender.android.ui.screens.auth.LoginScreen
import com.quickspender.android.ui.screens.auth.RegisterScreen
import com.quickspender.android.ui.screens.auth.AuthViewModel
import com.quickspender.android.ui.screens.main.MainScreen

@Composable
fun QuickSpenderNavigation(
    modifier: Modifier = Modifier,
    navController: NavHostController = rememberNavController(),
    authViewModel: AuthViewModel = hiltViewModel()
) {
    val isLoggedIn by authViewModel.isLoggedIn.collectAsState(initial = false)

    println("QuickSpenderNavigation: isLoggedIn = $isLoggedIn")

    // Navigate to main screen when user logs in
    LaunchedEffect(isLoggedIn) {
        if (isLoggedIn) {
            println("QuickSpenderNavigation: User logged in, navigating to main")
            navController.navigate("main") {
                popUpTo("login") { inclusive = true }
            }
        } else {
            println("QuickSpenderNavigation: User not logged in, navigating to login")
            navController.navigate("login") {
                popUpTo("main") { inclusive = true }
            }
        }
    }

    NavHost(
        navController = navController,
        startDestination = "login", // Always start with login, let LaunchedEffect handle navigation
        modifier = modifier
    ) {
        composable("login") {
            LoginScreen(
                onNavigateToRegister = {
                    navController.navigate("register")
                },
                onLoginSuccess = {
                    navController.navigate("main") {
                        popUpTo("login") { inclusive = true }
                    }
                }
            )
        }
        
        composable("register") {
            RegisterScreen(
                onNavigateToLogin = {
                    navController.popBackStack()
                },
                onRegisterSuccess = {
                    navController.navigate("main") {
                        popUpTo("register") { inclusive = true }
                    }
                }
            )
        }
        
        composable("main") {
            MainScreen(
                onLogout = {
                    navController.navigate("login") {
                        popUpTo("main") { inclusive = true }
                    }
                }
            )
        }
    }
}
