{"logs": [{"outputFile": "com.quickspender.android.app-mergeDebugResources-66:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d3887cc792a4ad23cc02050b37b01e71\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "279", "startColumns": "4", "startOffsets": "17914", "endColumns": "49", "endOffsets": "17959"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e66720b802ae0ceed44631764c9d9b5e\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "319", "startColumns": "4", "startOffsets": "20559", "endColumns": "82", "endOffsets": "20637"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\079bacef6faeb9f9a455732eb9607686\\transformed\\navigation-common-2.8.4\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3001,3014,3020,3026,3035", "startColumns": "4,4,4,4,4", "startOffsets": "166328,166967,167211,167458,167821", "endLines": "3013,3019,3025,3028,3039", "endColumns": "24,24,24,24,24", "endOffsets": "166962,167206,167453,167586,167998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2a5446c44aa521f2373650cce03072da\\transformed\\fragment-1.5.4\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "244,257,280,2752,2757", "startColumns": "4,4,4,4,4", "startOffsets": "16110,16755,17964,158684,158854", "endLines": "244,257,280,2756,2760", "endColumns": "56,64,63,24,24", "endOffsets": "16162,16815,18023,158849,158998"}}, {"source": "D:\\Startups\\QuickSpender\\android\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "320", "startColumns": "4", "startOffsets": "20642", "endColumns": "49", "endOffsets": "20687"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f011758bbd5e1f3717f8904866ac8535\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,18,19,21,22,24,25,26,27,28,29,30,31,35,36,37,38,39,40,42,43,44,45,47,48,49,50,51,52,53,54,55,56,57,58,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,170,171,172,173,174,175,176,177,178,194,195,196,197,198,199,200,201,237,238,239,240,247,254,255,258,275,282,283,284,285,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,402,413,414,415,416,417,418,426,427,431,435,439,444,450,457,461,465,470,474,478,482,486,490,494,500,504,510,514,520,524,529,533,536,540,546,550,556,560,566,569,573,577,581,585,589,590,591,592,595,598,601,604,608,609,610,611,612,615,617,619,621,626,627,631,637,641,642,644,656,657,661,667,671,672,673,677,704,708,709,713,741,913,939,1110,1136,1167,1175,1181,1197,1219,1224,1229,1239,1248,1257,1261,1268,1287,1294,1295,1304,1307,1310,1314,1318,1322,1325,1326,1331,1336,1346,1351,1358,1364,1365,1368,1372,1377,1379,1381,1384,1387,1389,1393,1396,1403,1406,1409,1413,1415,1419,1421,1423,1425,1429,1437,1445,1457,1463,1472,1475,1486,1489,1490,1495,1496,1524,1593,1663,1664,1674,1683,1684,1686,1690,1693,1696,1699,1702,1705,1708,1711,1715,1718,1721,1724,1728,1731,1735,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1761,1763,1764,1765,1766,1767,1768,1769,1770,1772,1773,1775,1776,1778,1780,1781,1783,1784,1785,1786,1787,1788,1790,1791,1792,1793,1794,1806,1808,1810,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1826,1827,1828,1829,1830,1831,1832,1834,1838,1845,1846,1847,1848,1849,1850,1854,1855,1856,1857,1859,1861,1863,1865,1867,1868,1869,1870,1872,1874,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1887,1890,1891,1892,1893,1895,1897,1898,1900,1901,1903,1905,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1920,1921,1922,1923,1925,1926,1927,1928,1929,1931,1933,1935,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1954,2029,2032,2035,2038,2052,2065,2107,2110,2139,2166,2175,2239,2602,2612,2650,2678,2800,2824,2830,2836,2857,2981,3040,3046,3050,3056,3091,3123,3189,3209,3264,3276,3302", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,359,400,455,517,581,651,712,787,863,940,1178,1263,1397,1473,1591,1668,1746,1852,1958,2037,2117,2174,2413,2487,2562,2627,2693,2753,2862,2934,3007,3074,3191,3250,3309,3368,3427,3486,3540,3594,3647,3701,3755,3809,3995,4069,4148,4221,4295,4366,4438,4510,4583,4640,4698,4771,4845,4919,4994,5066,5139,5209,5280,5340,5443,5512,5581,5651,5725,5801,5865,5942,6018,6095,6160,6229,6306,6381,6450,6518,6595,6661,6722,6819,6884,6953,7052,7123,7182,7240,7297,7356,7420,7491,7563,7635,7707,7779,7846,7914,7982,8041,8104,8168,8258,8349,8409,8475,8542,8608,8678,8742,8795,8862,8923,8990,9103,9161,9224,9289,9354,9429,9502,9574,9618,9665,9711,9760,9821,9882,9943,10005,10069,10133,10197,10262,10325,10385,10446,10512,10571,10631,10693,10764,10824,11380,11466,11553,11643,11730,11818,11900,11983,12073,13142,13194,13252,13297,13363,13427,13484,13541,15718,15775,15823,15872,16284,16617,16664,16820,17725,18081,18145,18207,18267,18693,18767,18837,18915,18969,19039,19124,19172,19218,19279,19342,19408,19472,19543,19606,19671,19735,19796,19857,19909,19982,20056,20125,20200,20274,20348,20489,26475,27057,27135,27225,27313,27409,27499,28081,28170,28417,28698,28950,29235,29628,30105,30327,30549,30825,31052,31282,31512,31742,31972,32199,32618,32844,33269,33499,33927,34146,34429,34637,34768,34995,35421,35646,36073,36294,36719,36839,37115,37416,37740,38031,38345,38482,38613,38718,38960,39127,39331,39539,39810,39922,40034,40139,40256,40470,40616,40756,40842,41190,41278,41524,41942,42191,42273,42371,43028,43128,43380,43804,44059,44153,44242,44479,46503,46745,46847,47100,49256,59937,61453,72148,73676,75433,76059,76479,77740,79005,79261,79497,80044,80538,81143,81341,81921,83289,83664,83782,84320,84477,84673,84946,85202,85372,85513,85577,85942,86309,86985,87249,87587,87940,88034,88220,88526,88788,88913,89040,89279,89490,89609,89802,89979,90434,90615,90737,90996,91109,91296,91398,91505,91634,91909,92417,92913,93790,94084,94654,94803,95535,95707,95791,96127,96219,97783,103014,108385,108447,109025,109609,109700,109813,110042,110202,110354,110525,110691,110860,111027,111190,111433,111603,111776,111947,112221,112420,112625,112955,113039,113135,113231,113329,113429,113531,113633,113735,113837,113939,114039,114135,114247,114376,114499,114630,114761,114859,114973,115067,115207,115341,115437,115549,115649,115765,115861,115973,116073,116213,116349,116513,116643,116801,116951,117092,117236,117371,117483,117633,117761,117889,118025,118157,118287,118417,118529,119427,119573,119717,119855,119921,120011,120087,120191,120281,120383,120491,120599,120699,120779,120871,120969,121079,121131,121209,121315,121407,121511,121621,121743,121906,122163,122243,122343,122433,122543,122633,122874,122968,123074,123166,123266,123378,123492,123608,123724,123818,123932,124044,124146,124266,124388,124470,124574,124694,124820,124918,125012,125100,125212,125328,125450,125562,125737,125853,125939,126031,126143,126267,126334,126460,126528,126656,126800,126928,126997,127092,127207,127320,127419,127528,127639,127750,127851,127956,128056,128186,128277,128400,128494,128606,128692,128796,128892,128980,129098,129202,129306,129432,129520,129628,129728,129818,129928,130012,130114,130198,130252,130316,130422,130508,130618,130702,130961,133577,133695,133810,133890,134251,134788,136192,136270,137614,138975,139363,142206,152259,152597,154268,155625,159852,160603,160865,161065,161444,165722,168003,168232,168383,168598,169681,170531,173557,174301,176432,176772,178083", "endLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,18,19,21,22,24,25,26,27,28,29,30,31,35,36,37,38,39,40,42,43,44,45,47,48,49,50,51,52,53,54,55,56,57,58,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,170,171,172,173,174,175,176,177,178,194,195,196,197,198,199,200,201,237,238,239,240,247,254,255,258,275,282,283,284,285,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,402,413,414,415,416,417,425,426,430,434,438,443,449,456,460,464,469,473,477,481,485,489,493,499,503,509,513,519,523,528,532,535,539,545,549,555,559,565,568,572,576,580,584,588,589,590,591,594,597,600,603,607,608,609,610,611,614,616,618,620,625,626,630,636,640,641,643,655,656,660,666,670,671,672,676,703,707,708,712,740,912,938,1109,1135,1166,1174,1180,1196,1218,1223,1228,1238,1247,1256,1260,1267,1286,1293,1294,1303,1306,1309,1313,1317,1321,1324,1325,1330,1335,1345,1350,1357,1363,1364,1367,1371,1376,1378,1380,1383,1386,1388,1392,1395,1402,1405,1408,1412,1414,1418,1420,1422,1424,1428,1436,1444,1456,1462,1471,1474,1485,1488,1489,1494,1495,1500,1592,1662,1663,1673,1682,1683,1685,1689,1692,1695,1698,1701,1704,1707,1710,1714,1717,1720,1723,1727,1730,1734,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1760,1762,1763,1764,1765,1766,1767,1768,1769,1771,1772,1774,1775,1777,1779,1780,1782,1783,1784,1785,1786,1787,1789,1790,1791,1792,1793,1794,1807,1809,1811,1812,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1825,1826,1827,1828,1829,1830,1831,1833,1837,1841,1845,1846,1847,1848,1849,1853,1854,1855,1856,1858,1860,1862,1864,1866,1867,1868,1869,1871,1873,1875,1876,1877,1878,1879,1880,1881,1882,1883,1884,1885,1886,1889,1890,1891,1892,1894,1896,1897,1899,1900,1902,1904,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1919,1920,1921,1922,1924,1925,1926,1927,1928,1930,1932,1934,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,2028,2031,2034,2037,2051,2057,2074,2109,2138,2165,2174,2238,2601,2605,2639,2677,2695,2823,2829,2835,2856,2980,3000,3045,3049,3055,3090,3102,3188,3208,3263,3275,3301,3308", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,294,395,450,512,576,646,707,782,858,935,1013,1258,1340,1468,1544,1663,1741,1847,1953,2032,2112,2169,2227,2482,2557,2622,2688,2748,2809,2929,3002,3069,3137,3245,3304,3363,3422,3481,3535,3589,3642,3696,3750,3804,3858,4064,4143,4216,4290,4361,4433,4505,4578,4635,4693,4766,4840,4914,4989,5061,5134,5204,5275,5335,5396,5507,5576,5646,5720,5796,5860,5937,6013,6090,6155,6224,6301,6376,6445,6513,6590,6656,6717,6814,6879,6948,7047,7118,7177,7235,7292,7351,7415,7486,7558,7630,7702,7774,7841,7909,7977,8036,8099,8163,8253,8344,8404,8470,8537,8603,8673,8737,8790,8857,8918,8985,9098,9156,9219,9284,9349,9424,9497,9569,9613,9660,9706,9755,9816,9877,9938,10000,10064,10128,10192,10257,10320,10380,10441,10507,10566,10626,10688,10759,10819,10887,11461,11548,11638,11725,11813,11895,11978,12068,12159,13189,13247,13292,13358,13422,13479,13536,13590,15770,15818,15867,15918,16313,16659,16708,16861,17752,18140,18202,18262,18319,18762,18832,18910,18964,19034,19119,19167,19213,19274,19337,19403,19467,19538,19601,19666,19730,19791,19852,19904,19977,20051,20120,20195,20269,20343,20484,20554,26523,27130,27220,27308,27404,27494,28076,28165,28412,28693,28945,29230,29623,30100,30322,30544,30820,31047,31277,31507,31737,31967,32194,32613,32839,33264,33494,33922,34141,34424,34632,34763,34990,35416,35641,36068,36289,36714,36834,37110,37411,37735,38026,38340,38477,38608,38713,38955,39122,39326,39534,39805,39917,40029,40134,40251,40465,40611,40751,40837,41185,41273,41519,41937,42186,42268,42366,43023,43123,43375,43799,44054,44148,44237,44474,46498,46740,46842,47095,49251,59932,61448,72143,73671,75428,76054,76474,77735,79000,79256,79492,80039,80533,81138,81336,81916,83284,83659,83777,84315,84472,84668,84941,85197,85367,85508,85572,85937,86304,86980,87244,87582,87935,88029,88215,88521,88783,88908,89035,89274,89485,89604,89797,89974,90429,90610,90732,90991,91104,91291,91393,91500,91629,91904,92412,92908,93785,94079,94649,94798,95530,95702,95786,96122,96214,96492,103009,108380,108442,109020,109604,109695,109808,110037,110197,110349,110520,110686,110855,111022,111185,111428,111598,111771,111942,112216,112415,112620,112950,113034,113130,113226,113324,113424,113526,113628,113730,113832,113934,114034,114130,114242,114371,114494,114625,114756,114854,114968,115062,115202,115336,115432,115544,115644,115760,115856,115968,116068,116208,116344,116508,116638,116796,116946,117087,117231,117366,117478,117628,117756,117884,118020,118152,118282,118412,118524,118664,119568,119712,119850,119916,120006,120082,120186,120276,120378,120486,120594,120694,120774,120866,120964,121074,121126,121204,121310,121402,121506,121616,121738,121901,122058,122238,122338,122428,122538,122628,122869,122963,123069,123161,123261,123373,123487,123603,123719,123813,123927,124039,124141,124261,124383,124465,124569,124689,124815,124913,125007,125095,125207,125323,125445,125557,125732,125848,125934,126026,126138,126262,126329,126455,126523,126651,126795,126923,126992,127087,127202,127315,127414,127523,127634,127745,127846,127951,128051,128181,128272,128395,128489,128601,128687,128791,128887,128975,129093,129197,129301,129427,129515,129623,129723,129813,129923,130007,130109,130193,130247,130311,130417,130503,130613,130697,130817,133572,133690,133805,133885,134246,134479,135300,136265,137609,138970,139358,142201,152254,152389,153962,155620,156192,160598,160860,161060,161439,165717,166323,168227,168378,168593,169676,169988,173552,174296,176427,176767,178078,178281"}}, {"source": "D:\\Startups\\QuickSpender\\android\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "9,2,8,6,7,3", "startColumns": "4,4,4,4,4,4", "startOffsets": "339,55,289,192,240,97", "endColumns": "51,41,49,47,48,41", "endOffsets": "386,92,334,235,284,134"}, "to": {"startLines": "20,23,34,41,46,81", "startColumns": "4,4,4,4,4,4", "startOffsets": "1345,1549,2363,2814,3142,5401", "endColumns": "51,41,49,47,48,41", "endOffsets": "1392,1586,2408,2857,3186,5438"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9a8b0c355848d54770b025949c6a018f\\transformed\\navigation-runtime-2.8.4\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "252,2058,3029,3032", "startColumns": "4,4,4,4", "startOffsets": "16497,134484,167591,167706", "endLines": "252,2064,3031,3034", "endColumns": "52,24,24,24", "endOffsets": "16545,134783,167701,167816"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\59944556d3ccf5c33c2cc757963b127f\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "278", "startColumns": "4", "startOffsets": "17860", "endColumns": "53", "endOffsets": "17909"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\668b07b585425ac50b3664ae01d10c25\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "411,412", "startColumns": "4,4", "startOffsets": "26946,27002", "endColumns": "55,54", "endOffsets": "26997,27052"}}, {"source": "D:\\Startups\\QuickSpender\\android\\app\\src\\main\\res\\values\\config.xml", "from": {"startLines": "10,9,11,12,3,6", "startColumns": "4,4,4,4,4,4", "startOffsets": "375,318,435,489,88,201", "endColumns": "59,56,53,54,73,80", "endOffsets": "430,370,484,539,157,277"}, "to": {"startLines": "286,288,289,291,330,399", "startColumns": "4,4,4,4,4,4", "startOffsets": "18324,18457,18514,18638,21322,26290", "endColumns": "59,56,53,54,73,80", "endOffsets": "18379,18509,18563,18688,21391,26366"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0312a6e9ced2c28e9a6c102442d7dd98\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "276", "startColumns": "4", "startOffsets": "17757", "endColumns": "42", "endOffsets": "17795"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e6ee2619a776087357431d65cec3fd3a\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "287,336,337,338,339,340,341,342,343,344,345,348,349,350,351,352,353,354,355,356,357,358,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,1504,1514", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18384,21733,21821,21907,21988,22072,22141,22206,22289,22395,22481,22601,22655,22724,22785,22854,22943,23038,23112,23209,23302,23400,23549,23640,23728,23824,23922,23986,24054,24141,24235,24302,24374,24446,24547,24656,24732,24801,24849,24915,24979,25053,25110,25167,25239,25289,25343,25414,25485,25555,25624,25682,25758,25829,25903,25989,26039,26109,96609,97324", "endLines": "287,336,337,338,339,340,341,342,343,344,347,348,349,350,351,352,353,354,355,356,357,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,1513,1516", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "18452,21816,21902,21983,22067,22136,22201,22284,22390,22476,22596,22650,22719,22780,22849,22938,23033,23107,23204,23297,23395,23544,23635,23723,23819,23917,23981,24049,24136,24230,24297,24369,24441,24542,24651,24727,24796,24844,24910,24974,25048,25105,25162,25234,25284,25338,25409,25480,25550,25619,25677,25753,25824,25898,25984,26034,26104,26169,97319,97472"}}, {"source": "D:\\Startups\\QuickSpender\\android\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "103", "endLines": "5", "endColumns": "12", "endOffsets": "253"}, "to": {"startLines": "1842", "startColumns": "4", "startOffsets": "122063", "endLines": "1844", "endColumns": "12", "endOffsets": "122158"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\de56ff69481e934d0ac0a84174a747e5\\transformed\\appcompat-resources-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2075,2091,2097,3103,3119", "startColumns": "4,4,4,4,4", "startOffsets": "135305,135730,135908,169993,170404", "endLines": "2090,2096,2106,3118,3122", "endColumns": "24,24,24,24,24", "endOffsets": "135725,135903,136187,170399,170526"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\683384900b47375005ae7f584e543a91\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "245", "startColumns": "4", "startOffsets": "16167", "endColumns": "65", "endOffsets": "16228"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5bb51c3d43641a7411f79d0d3d31b1e5\\transformed\\activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "256,277", "startColumns": "4,4", "startOffsets": "16713,17800", "endColumns": "41,59", "endOffsets": "16750,17855"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7e5c272ed8753b7bca75d20a7afd60fc\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,241,242,243,246,248,281,328,329,331,332,333,334,335,397,398,400,401,403,404,405,406,408,409,410,1501,1517,1520", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13808,13867,13926,13986,14046,14106,14166,14226,14286,14346,14406,14466,14526,14585,14645,14705,14765,14825,14885,14945,15005,15065,15125,15185,15244,15304,15364,15423,15482,15541,15600,15659,15923,15997,16055,16233,16318,18028,21203,21268,21396,21462,21563,21621,21673,26174,26236,26371,26421,26528,26574,26620,26662,26773,26820,26856,96497,97477,97588", "endLines": "205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,241,242,243,246,248,281,328,329,331,332,333,334,335,397,398,400,401,403,404,405,406,408,409,410,1503,1519,1523", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "13862,13921,13981,14041,14101,14161,14221,14281,14341,14401,14461,14521,14580,14640,14700,14760,14820,14880,14940,15000,15060,15120,15180,15239,15299,15359,15418,15477,15536,15595,15654,15713,15992,16050,16105,16279,16368,18076,21263,21317,21457,21558,21616,21668,21728,26231,26285,26416,26470,26569,26615,26657,26697,26815,26851,26941,96604,97583,97778"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9c8287efcc424c1657e6673c185d7f75\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "249,253", "startColumns": "4,4", "startOffsets": "16373,16550", "endColumns": "53,66", "endOffsets": "16422,16612"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ffc69b87a74cd983f3333cb07d0c197f\\transformed\\core-1.15.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,157,178,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8915,9596,10278", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,156,177,210,216", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8910,9591,10273,10440"}, "to": {"startLines": "5,16,17,32,33,59,60,163,164,165,166,167,168,169,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,202,203,204,250,251,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,290,321,322,323,324,325,326,327,407,1795,1796,1800,1801,1805,1952,1953,2606,2640,2696,2731,2761,2794", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,1018,1090,2232,2297,3863,3932,10892,10962,11030,11102,11172,11233,11307,12164,12225,12286,12348,12412,12474,12535,12603,12703,12763,12829,12902,12971,13028,13080,13595,13667,13743,16427,16462,16866,16921,16984,17039,17097,17155,17216,17279,17336,17387,17437,17498,17555,17621,17655,17690,18568,20692,20759,20831,20900,20969,21043,21115,26702,118669,118786,118987,119097,119298,130822,130894,152394,153967,156197,158003,159003,159685", "endLines": "5,16,17,32,33,59,60,163,164,165,166,167,168,169,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,202,203,204,250,251,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,290,321,322,323,324,325,326,327,407,1795,1799,1800,1804,1805,1952,1953,2611,2649,2730,2751,2793,2799", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "354,1085,1173,2292,2358,3927,3990,10957,11025,11097,11167,11228,11302,11375,12220,12281,12343,12407,12469,12530,12598,12698,12758,12824,12897,12966,13023,13075,13137,13662,13738,13803,16457,16492,16916,16979,17034,17092,17150,17211,17274,17331,17382,17432,17493,17550,17616,17650,17685,17720,18633,20754,20826,20895,20964,21038,21110,21198,26768,118781,118982,119092,119293,119422,130889,130956,152592,154263,157998,158679,159680,159847"}}]}]}