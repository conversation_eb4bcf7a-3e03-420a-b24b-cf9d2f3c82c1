{"logs": [{"outputFile": "com.quickspender.android.app-mergeDebugResources-68:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\d3887cc792a4ad23cc02050b37b01e71\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "288", "startColumns": "4", "startOffsets": "18310", "endColumns": "49", "endOffsets": "18355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e66720b802ae0ceed44631764c9d9b5e\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "328", "startColumns": "4", "startOffsets": "20955", "endColumns": "82", "endOffsets": "21033"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\079bacef6faeb9f9a455732eb9607686\\transformed\\navigation-common-2.8.4\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3020,3033,3039,3045,3054", "startColumns": "4,4,4,4,4", "startOffsets": "167051,167690,167934,168181,168544", "endLines": "3032,3038,3044,3047,3058", "endColumns": "24,24,24,24,24", "endOffsets": "167685,167929,168176,168309,168721"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2a5446c44aa521f2373650cce03072da\\transformed\\fragment-1.5.4\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "253,266,289,2771,2776", "startColumns": "4,4,4,4,4", "startOffsets": "16506,17151,18360,159407,159577", "endLines": "253,266,289,2775,2779", "endColumns": "56,64,63,24,24", "endOffsets": "16558,17211,18419,159572,159721"}}, {"source": "D:\\Startups\\QuickSpender\\android\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "329", "startColumns": "4", "startOffsets": "21038", "endColumns": "49", "endOffsets": "21083"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f011758bbd5e1f3717f8904866ac8535\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,4,14,15,16,17,18,19,20,21,22,23,26,27,29,30,32,33,34,35,36,37,38,39,43,44,45,46,47,48,50,51,52,53,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,202,203,204,205,206,207,208,209,245,246,247,248,256,263,264,267,284,291,292,293,294,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,411,422,423,424,425,426,427,435,436,440,444,448,453,459,466,470,474,479,483,487,491,495,499,503,509,513,519,523,529,533,538,542,545,549,555,559,565,569,575,578,582,586,590,594,598,599,600,601,604,607,610,613,617,618,619,620,621,624,626,628,630,635,636,640,646,650,651,653,665,666,670,676,680,681,682,686,713,717,718,722,750,922,948,1119,1145,1176,1184,1190,1206,1228,1233,1238,1248,1257,1266,1270,1277,1296,1303,1304,1313,1316,1319,1323,1327,1331,1334,1335,1340,1345,1355,1360,1367,1373,1374,1377,1381,1386,1388,1390,1393,1396,1398,1402,1405,1412,1415,1418,1422,1424,1428,1430,1432,1434,1438,1446,1454,1466,1472,1481,1484,1495,1498,1499,1504,1505,1533,1602,1672,1673,1683,1692,1693,1695,1699,1702,1705,1708,1711,1714,1717,1720,1724,1727,1730,1733,1737,1740,1744,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1770,1772,1773,1774,1775,1776,1777,1778,1779,1781,1782,1784,1785,1787,1789,1790,1792,1793,1794,1795,1796,1797,1799,1800,1801,1802,1803,1815,1817,1819,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1835,1836,1837,1838,1839,1840,1841,1843,1847,1854,1855,1856,1857,1858,1859,1863,1864,1865,1866,1868,1870,1872,1874,1876,1877,1878,1879,1881,1883,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1896,1899,1900,1901,1902,1904,1906,1907,1909,1910,1912,1914,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1927,1929,1930,1931,1932,1934,1935,1936,1937,1938,1940,1942,1944,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1963,2038,2041,2044,2047,2061,2084,2126,2129,2158,2185,2194,2258,2621,2631,2669,2697,2819,2843,2849,2855,2876,3000,3059,3065,3069,3075,3110,3170,3236,3256,3311,3323,3349", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,695,736,791,853,917,987,1048,1123,1199,1276,1514,1599,1733,1809,1927,2004,2082,2188,2294,2373,2453,2510,2749,2823,2898,2963,3029,3089,3198,3270,3343,3410,3527,3586,3645,3704,3763,3822,3876,3930,3983,4037,4091,4145,4331,4405,4484,4557,4631,4702,4774,4846,4919,4976,5034,5107,5181,5255,5330,5402,5475,5545,5616,5676,5779,5848,5917,5987,6061,6137,6201,6278,6354,6431,6496,6565,6642,6717,6786,6854,6931,6997,7058,7155,7220,7289,7388,7459,7518,7576,7633,7692,7756,7827,7899,7971,8043,8115,8182,8250,8318,8377,8440,8504,8594,8685,8745,8811,8878,8944,9014,9078,9131,9198,9259,9326,9439,9497,9560,9625,9690,9765,9838,9910,9954,10001,10047,10096,10157,10218,10279,10341,10405,10469,10533,10598,10661,10721,10782,10848,10907,10967,11029,11100,11160,11716,11802,11889,11979,12066,12154,12236,12319,12409,13478,13530,13588,13633,13699,13763,13820,13877,16054,16111,16159,16208,16680,17013,17060,17216,18121,18477,18541,18603,18663,19089,19163,19233,19311,19365,19435,19520,19568,19614,19675,19738,19804,19868,19939,20002,20067,20131,20192,20253,20305,20378,20452,20521,20596,20670,20744,20885,26872,27454,27532,27622,27710,27806,27896,28478,28567,28814,29095,29347,29632,30025,30502,30724,30946,31222,31449,31679,31909,32139,32369,32596,33015,33241,33666,33896,34324,34543,34826,35034,35165,35392,35818,36043,36470,36691,37116,37236,37512,37813,38137,38428,38742,38879,39010,39115,39357,39524,39728,39936,40207,40319,40431,40536,40653,40867,41013,41153,41239,41587,41675,41921,42339,42588,42670,42768,43425,43525,43777,44201,44456,44550,44639,44876,46900,47142,47244,47497,49653,60334,61850,72545,74073,75830,76456,76876,78137,79402,79658,79894,80441,80935,81540,81738,82318,83686,84061,84179,84717,84874,85070,85343,85599,85769,85910,85974,86339,86706,87382,87646,87984,88337,88431,88617,88923,89185,89310,89437,89676,89887,90006,90199,90376,90831,91012,91134,91393,91506,91693,91795,91902,92031,92306,92814,93310,94187,94481,95051,95200,95932,96104,96188,96524,96616,98180,103411,108782,108844,109422,110006,110097,110210,110439,110599,110751,110922,111088,111257,111424,111587,111830,112000,112173,112344,112618,112817,113022,113352,113436,113532,113628,113726,113826,113928,114030,114132,114234,114336,114436,114532,114644,114773,114896,115027,115158,115256,115370,115464,115604,115738,115834,115946,116046,116162,116258,116370,116470,116610,116746,116910,117040,117198,117348,117489,117633,117768,117880,118030,118158,118286,118422,118554,118684,118814,118926,119824,119970,120114,120252,120318,120408,120484,120588,120678,120780,120888,120996,121096,121176,121268,121366,121476,121528,121606,121712,121804,121908,122018,122140,122303,122560,122640,122740,122830,122940,123030,123271,123365,123471,123563,123663,123775,123889,124005,124121,124215,124329,124441,124543,124663,124785,124867,124971,125091,125217,125315,125409,125497,125609,125725,125847,125959,126134,126250,126336,126428,126540,126664,126731,126857,126925,127053,127197,127325,127394,127489,127604,127717,127816,127925,128036,128147,128248,128353,128453,128583,128674,128797,128891,129003,129089,129193,129289,129377,129495,129599,129703,129829,129917,130025,130125,130215,130325,130409,130511,130595,130649,130713,130819,130905,131015,131099,131358,133974,134092,134207,134287,134648,135511,136915,136993,138337,139698,140086,142929,152982,153320,154991,156348,160575,161326,161588,161788,162167,166445,168726,168955,169106,169321,170404,172285,175311,176055,178186,178526,179837", "endLines": "2,3,4,14,15,16,17,18,19,20,21,22,23,26,27,29,30,32,33,34,35,36,37,38,39,43,44,45,46,47,48,50,51,52,53,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,202,203,204,205,206,207,208,209,245,246,247,248,256,263,264,267,284,291,292,293,294,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,411,422,423,424,425,426,434,435,439,443,447,452,458,465,469,473,478,482,486,490,494,498,502,508,512,518,522,528,532,537,541,544,548,554,558,564,568,574,577,581,585,589,593,597,598,599,600,603,606,609,612,616,617,618,619,620,623,625,627,629,634,635,639,645,649,650,652,664,665,669,675,679,680,681,685,712,716,717,721,749,921,947,1118,1144,1175,1183,1189,1205,1227,1232,1237,1247,1256,1265,1269,1276,1295,1302,1303,1312,1315,1318,1322,1326,1330,1333,1334,1339,1344,1354,1359,1366,1372,1373,1376,1380,1385,1387,1389,1392,1395,1397,1401,1404,1411,1414,1417,1421,1423,1427,1429,1431,1433,1437,1445,1453,1465,1471,1480,1483,1494,1497,1498,1503,1504,1509,1601,1671,1672,1682,1691,1692,1694,1698,1701,1704,1707,1710,1713,1716,1719,1723,1726,1729,1732,1736,1739,1743,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1769,1771,1772,1773,1774,1775,1776,1777,1778,1780,1781,1783,1784,1786,1788,1789,1791,1792,1793,1794,1795,1796,1798,1799,1800,1801,1802,1803,1816,1818,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1834,1835,1836,1837,1838,1839,1840,1842,1846,1850,1854,1855,1856,1857,1858,1862,1863,1864,1865,1867,1869,1871,1873,1875,1876,1877,1878,1880,1882,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1898,1899,1900,1901,1903,1905,1906,1908,1909,1911,1913,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1928,1929,1930,1931,1933,1934,1935,1936,1937,1939,1941,1943,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,2037,2040,2043,2046,2060,2066,2093,2128,2157,2184,2193,2257,2620,2624,2658,2696,2714,2842,2848,2854,2875,2999,3019,3064,3068,3074,3109,3121,3235,3255,3310,3322,3348,3355", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,294,731,786,848,912,982,1043,1118,1194,1271,1349,1594,1676,1804,1880,1999,2077,2183,2289,2368,2448,2505,2563,2818,2893,2958,3024,3084,3145,3265,3338,3405,3473,3581,3640,3699,3758,3817,3871,3925,3978,4032,4086,4140,4194,4400,4479,4552,4626,4697,4769,4841,4914,4971,5029,5102,5176,5250,5325,5397,5470,5540,5611,5671,5732,5843,5912,5982,6056,6132,6196,6273,6349,6426,6491,6560,6637,6712,6781,6849,6926,6992,7053,7150,7215,7284,7383,7454,7513,7571,7628,7687,7751,7822,7894,7966,8038,8110,8177,8245,8313,8372,8435,8499,8589,8680,8740,8806,8873,8939,9009,9073,9126,9193,9254,9321,9434,9492,9555,9620,9685,9760,9833,9905,9949,9996,10042,10091,10152,10213,10274,10336,10400,10464,10528,10593,10656,10716,10777,10843,10902,10962,11024,11095,11155,11223,11797,11884,11974,12061,12149,12231,12314,12404,12495,13525,13583,13628,13694,13758,13815,13872,13926,16106,16154,16203,16254,16709,17055,17104,17257,18148,18536,18598,18658,18715,19158,19228,19306,19360,19430,19515,19563,19609,19670,19733,19799,19863,19934,19997,20062,20126,20187,20248,20300,20373,20447,20516,20591,20665,20739,20880,20950,26920,27527,27617,27705,27801,27891,28473,28562,28809,29090,29342,29627,30020,30497,30719,30941,31217,31444,31674,31904,32134,32364,32591,33010,33236,33661,33891,34319,34538,34821,35029,35160,35387,35813,36038,36465,36686,37111,37231,37507,37808,38132,38423,38737,38874,39005,39110,39352,39519,39723,39931,40202,40314,40426,40531,40648,40862,41008,41148,41234,41582,41670,41916,42334,42583,42665,42763,43420,43520,43772,44196,44451,44545,44634,44871,46895,47137,47239,47492,49648,60329,61845,72540,74068,75825,76451,76871,78132,79397,79653,79889,80436,80930,81535,81733,82313,83681,84056,84174,84712,84869,85065,85338,85594,85764,85905,85969,86334,86701,87377,87641,87979,88332,88426,88612,88918,89180,89305,89432,89671,89882,90001,90194,90371,90826,91007,91129,91388,91501,91688,91790,91897,92026,92301,92809,93305,94182,94476,95046,95195,95927,96099,96183,96519,96611,96889,103406,108777,108839,109417,110001,110092,110205,110434,110594,110746,110917,111083,111252,111419,111582,111825,111995,112168,112339,112613,112812,113017,113347,113431,113527,113623,113721,113821,113923,114025,114127,114229,114331,114431,114527,114639,114768,114891,115022,115153,115251,115365,115459,115599,115733,115829,115941,116041,116157,116253,116365,116465,116605,116741,116905,117035,117193,117343,117484,117628,117763,117875,118025,118153,118281,118417,118549,118679,118809,118921,119061,119965,120109,120247,120313,120403,120479,120583,120673,120775,120883,120991,121091,121171,121263,121361,121471,121523,121601,121707,121799,121903,122013,122135,122298,122455,122635,122735,122825,122935,123025,123266,123360,123466,123558,123658,123770,123884,124000,124116,124210,124324,124436,124538,124658,124780,124862,124966,125086,125212,125310,125404,125492,125604,125720,125842,125954,126129,126245,126331,126423,126535,126659,126726,126852,126920,127048,127192,127320,127389,127484,127599,127712,127811,127920,128031,128142,128243,128348,128448,128578,128669,128792,128886,128998,129084,129188,129284,129372,129490,129594,129698,129824,129912,130020,130120,130210,130320,130404,130506,130590,130644,130708,130814,130900,131010,131094,131214,133969,134087,134202,134282,134643,134876,136023,136988,138332,139693,140081,142924,152977,153112,154685,156343,156915,161321,161583,161783,162162,166440,167046,168950,169101,169316,170399,170711,175306,176050,178181,178521,179832,180035"}}, {"source": "D:\\Startups\\QuickSpender\\android\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "9,2,8,6,7,3", "startColumns": "4,4,4,4,4,4", "startOffsets": "339,55,289,192,240,97", "endColumns": "51,41,49,47,48,41", "endOffsets": "386,92,334,235,284,134"}, "to": {"startLines": "28,31,42,49,54,89", "startColumns": "4,4,4,4,4,4", "startOffsets": "1681,1885,2699,3150,3478,5737", "endColumns": "51,41,49,47,48,41", "endOffsets": "1728,1922,2744,3193,3522,5774"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9a8b0c355848d54770b025949c6a018f\\transformed\\navigation-runtime-2.8.4\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "261,2073,3048,3051", "startColumns": "4,4,4,4", "startOffsets": "16893,135076,168314,168429", "endLines": "261,2079,3050,3053", "endColumns": "52,24,24,24", "endOffsets": "16941,135375,168424,168539"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\59944556d3ccf5c33c2cc757963b127f\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "287", "startColumns": "4", "startOffsets": "18256", "endColumns": "53", "endOffsets": "18305"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\668b07b585425ac50b3664ae01d10c25\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "420,421", "startColumns": "4,4", "startOffsets": "27343,27399", "endColumns": "55,54", "endOffsets": "27394,27449"}}, {"source": "D:\\Startups\\QuickSpender\\android\\app\\src\\main\\res\\values\\config.xml", "from": {"startLines": "10,9,11,12,3,6", "startColumns": "4,4,4,4,4,4", "startOffsets": "376,319,436,490,88,202", "endColumns": "59,56,53,54,74,80", "endOffsets": "431,371,485,540,158,278"}, "to": {"startLines": "295,297,298,300,339,408", "startColumns": "4,4,4,4,4,4", "startOffsets": "18720,18853,18910,19034,21718,26687", "endColumns": "59,56,53,54,74,80", "endOffsets": "18775,18905,18959,19084,21788,26763"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0312a6e9ced2c28e9a6c102442d7dd98\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "285", "startColumns": "4", "startOffsets": "18153", "endColumns": "42", "endOffsets": "18191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e6ee2619a776087357431d65cec3fd3a\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "296,345,346,347,348,349,350,351,352,353,354,357,358,359,360,361,362,363,364,365,366,367,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,1513,1523", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18780,22130,22218,22304,22385,22469,22538,22603,22686,22792,22878,22998,23052,23121,23182,23251,23340,23435,23509,23606,23699,23797,23946,24037,24125,24221,24319,24383,24451,24538,24632,24699,24771,24843,24944,25053,25129,25198,25246,25312,25376,25450,25507,25564,25636,25686,25740,25811,25882,25952,26021,26079,26155,26226,26300,26386,26436,26506,97006,97721", "endLines": "296,345,346,347,348,349,350,351,352,353,356,357,358,359,360,361,362,363,364,365,366,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,1522,1525", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "18848,22213,22299,22380,22464,22533,22598,22681,22787,22873,22993,23047,23116,23177,23246,23335,23430,23504,23601,23694,23792,23941,24032,24120,24216,24314,24378,24446,24533,24627,24694,24766,24838,24939,25048,25124,25193,25241,25307,25371,25445,25502,25559,25631,25681,25735,25806,25877,25947,26016,26074,26150,26221,26295,26381,26431,26501,26566,97716,97869"}}, {"source": "D:\\Startups\\QuickSpender\\android\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "103", "endLines": "5", "endColumns": "12", "endOffsets": "253"}, "to": {"startLines": "1851", "startColumns": "4", "startOffsets": "122460", "endLines": "1853", "endColumns": "12", "endOffsets": "122555"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\de56ff69481e934d0ac0a84174a747e5\\transformed\\appcompat-resources-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2094,2110,2116,3150,3166", "startColumns": "4,4,4,4,4", "startOffsets": "136028,136453,136631,171747,172158", "endLines": "2109,2115,2125,3165,3169", "endColumns": "24,24,24,24,24", "endOffsets": "136448,136626,136910,172153,172280"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\c9f7368b08ea7780a30cc4aa9ee47ccf\\transformed\\window-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,7,8,9,10,11,19,23,34,51", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,234,294,346,391,451,869,1066,1790,2904", "endLines": "6,7,8,9,10,18,22,33,50,58", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "229,289,341,386,446,864,1061,1785,2899,3292"}, "to": {"startLines": "6,11,12,13,250,2067,2080,3122,3130,3142", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "359,538,598,650,16333,134881,135380,170716,170998,171438", "endLines": "10,11,12,13,250,2072,2083,3129,3141,3149", "endColumns": "11,59,51,44,59,24,24,24,24,24", "endOffsets": "533,593,645,690,16388,135071,135506,170993,171433,171742"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\683384900b47375005ae7f584e543a91\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "254", "startColumns": "4", "startOffsets": "16563", "endColumns": "65", "endOffsets": "16624"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5bb51c3d43641a7411f79d0d3d31b1e5\\transformed\\activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "265,286", "startColumns": "4,4", "startOffsets": "17109,18196", "endColumns": "41,59", "endOffsets": "17146,18251"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7e5c272ed8753b7bca75d20a7afd60fc\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,249,251,252,255,257,290,337,338,340,341,342,343,344,406,407,409,410,412,413,414,415,417,418,419,1510,1526,1529", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14144,14203,14262,14322,14382,14442,14502,14562,14622,14682,14742,14802,14862,14921,14981,15041,15101,15161,15221,15281,15341,15401,15461,15521,15580,15640,15700,15759,15818,15877,15936,15995,16259,16393,16451,16629,16714,18424,21599,21664,21793,21859,21960,22018,22070,26571,26633,26768,26818,26925,26971,27017,27059,27170,27217,27253,96894,97874,97985", "endLines": "213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,249,251,252,255,257,290,337,338,340,341,342,343,344,406,407,409,410,412,413,414,415,417,418,419,1512,1528,1532", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "14198,14257,14317,14377,14437,14497,14557,14617,14677,14737,14797,14857,14916,14976,15036,15096,15156,15216,15276,15336,15396,15456,15516,15575,15635,15695,15754,15813,15872,15931,15990,16049,16328,16446,16501,16675,16764,18472,21659,21713,21854,21955,22013,22065,22125,26628,26682,26813,26867,26966,27012,27054,27094,27212,27248,27338,97001,97980,98175"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\9c8287efcc424c1657e6673c185d7f75\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "258,262", "startColumns": "4,4", "startOffsets": "16769,16946", "endColumns": "53,66", "endOffsets": "16818,17008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ffc69b87a74cd983f3333cb07d0c197f\\transformed\\core-1.15.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,157,178,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8915,9596,10278", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,156,177,210,216", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8910,9591,10273,10440"}, "to": {"startLines": "5,24,25,40,41,67,68,171,172,173,174,175,176,177,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,210,211,212,259,260,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,299,330,331,332,333,334,335,336,416,1804,1805,1809,1810,1814,1961,1962,2625,2659,2715,2750,2780,2813", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,1354,1426,2568,2633,4199,4268,11228,11298,11366,11438,11508,11569,11643,12500,12561,12622,12684,12748,12810,12871,12939,13039,13099,13165,13238,13307,13364,13416,13931,14003,14079,16823,16858,17262,17317,17380,17435,17493,17551,17612,17675,17732,17783,17833,17894,17951,18017,18051,18086,18964,21088,21155,21227,21296,21365,21439,21511,27099,119066,119183,119384,119494,119695,131219,131291,153117,154690,156920,158726,159726,160408", "endLines": "5,24,25,40,41,67,68,171,172,173,174,175,176,177,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,210,211,212,259,260,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,299,330,331,332,333,334,335,336,416,1804,1808,1809,1813,1814,1961,1962,2630,2668,2749,2770,2812,2818", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "354,1421,1509,2628,2694,4263,4326,11293,11361,11433,11503,11564,11638,11711,12556,12617,12679,12743,12805,12866,12934,13034,13094,13160,13233,13302,13359,13411,13473,13998,14074,14139,16853,16888,17312,17375,17430,17488,17546,17607,17670,17727,17778,17828,17889,17946,18012,18046,18081,18116,19029,21150,21222,21291,21360,21434,21506,21594,27165,119178,119379,119489,119690,119819,131286,131353,153315,154986,158721,159402,160403,160570"}}]}]}