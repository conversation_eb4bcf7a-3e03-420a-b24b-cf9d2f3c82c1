  Application android.app  Bundle android.app.Activity  Context android.content  Bundle android.content.Context  Bundle android.content.ContextWrapper  Bundle 
android.os  Bundle  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  Bundle #androidx.activity.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  	DataStore androidx.datastore.core  Preferences #androidx.datastore.preferences.core  	ViewModel androidx.lifecycle  AddTransactionUiState androidx.lifecycle.ViewModel  AuthRepository androidx.lifecycle.ViewModel  AuthUiState androidx.lifecycle.ViewModel  CategoryManagementUiState androidx.lifecycle.ViewModel  CategoryRepository androidx.lifecycle.ViewModel  DashboardUiState androidx.lifecycle.ViewModel  Double androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  ReportRepository androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  TransactionListUiState androidx.lifecycle.ViewModel  TransactionRepository androidx.lifecycle.ViewModel  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Embedded 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  RoomDatabase 
androidx.room  Update 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  CategoryDao androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  QuickSpenderDatabase androidx.room.RoomDatabase  TransactionDao androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  Gson com.google.gson  MainActivity com.quickspender.android  QuickSpenderApplication com.quickspender.android  Bundle %com.quickspender.android.MainActivity  AuthApi !com.quickspender.android.data.api  Body !com.quickspender.android.data.api  CategoryApi !com.quickspender.android.data.api  Int !com.quickspender.android.data.api  List !com.quickspender.android.data.api  Path !com.quickspender.android.data.api  Query !com.quickspender.android.data.api  	ReportApi !com.quickspender.android.data.api  String !com.quickspender.android.data.api  TransactionApi !com.quickspender.android.data.api  Unit !com.quickspender.android.data.api  AuthenticationResponse )com.quickspender.android.data.api.AuthApi  Body )com.quickspender.android.data.api.AuthApi  LoginRequest )com.quickspender.android.data.api.AuthApi  RegisterRequest )com.quickspender.android.data.api.AuthApi  Response )com.quickspender.android.data.api.AuthApi  Body -com.quickspender.android.data.api.CategoryApi  Category -com.quickspender.android.data.api.CategoryApi  CreateCategoryRequest -com.quickspender.android.data.api.CategoryApi  Int -com.quickspender.android.data.api.CategoryApi  List -com.quickspender.android.data.api.CategoryApi  Path -com.quickspender.android.data.api.CategoryApi  Query -com.quickspender.android.data.api.CategoryApi  Response -com.quickspender.android.data.api.CategoryApi  String -com.quickspender.android.data.api.CategoryApi  Unit -com.quickspender.android.data.api.CategoryApi  UpdateCategoryRequest -com.quickspender.android.data.api.CategoryApi  Query +com.quickspender.android.data.api.ReportApi  Response +com.quickspender.android.data.api.ReportApi  String +com.quickspender.android.data.api.ReportApi  SummaryResponse +com.quickspender.android.data.api.ReportApi  Body 0com.quickspender.android.data.api.TransactionApi  CreateTransactionRequest 0com.quickspender.android.data.api.TransactionApi  Int 0com.quickspender.android.data.api.TransactionApi  List 0com.quickspender.android.data.api.TransactionApi  Path 0com.quickspender.android.data.api.TransactionApi  Query 0com.quickspender.android.data.api.TransactionApi  Response 0com.quickspender.android.data.api.TransactionApi  String 0com.quickspender.android.data.api.TransactionApi  TransactionResponse 0com.quickspender.android.data.api.TransactionApi  Unit 0com.quickspender.android.data.api.TransactionApi  UpdateTransactionRequest 0com.quickspender.android.data.api.TransactionApi  Category #com.quickspender.android.data.local  QuickSpenderDatabase #com.quickspender.android.data.local  Transaction #com.quickspender.android.data.local  Volatile #com.quickspender.android.data.local  CategoryDao 8com.quickspender.android.data.local.QuickSpenderDatabase  Context 8com.quickspender.android.data.local.QuickSpenderDatabase  QuickSpenderDatabase 8com.quickspender.android.data.local.QuickSpenderDatabase  TransactionDao 8com.quickspender.android.data.local.QuickSpenderDatabase  Volatile 8com.quickspender.android.data.local.QuickSpenderDatabase  CategoryDao Bcom.quickspender.android.data.local.QuickSpenderDatabase.Companion  Context Bcom.quickspender.android.data.local.QuickSpenderDatabase.Companion  QuickSpenderDatabase Bcom.quickspender.android.data.local.QuickSpenderDatabase.Companion  TransactionDao Bcom.quickspender.android.data.local.QuickSpenderDatabase.Companion  Volatile Bcom.quickspender.android.data.local.QuickSpenderDatabase.Companion  CategoryDao 'com.quickspender.android.data.local.dao  Dao 'com.quickspender.android.data.local.dao  Delete 'com.quickspender.android.data.local.dao  Insert 'com.quickspender.android.data.local.dao  Int 'com.quickspender.android.data.local.dao  List 'com.quickspender.android.data.local.dao  OnConflictStrategy 'com.quickspender.android.data.local.dao  Query 'com.quickspender.android.data.local.dao  String 'com.quickspender.android.data.local.dao  TransactionDao 'com.quickspender.android.data.local.dao  Update 'com.quickspender.android.data.local.dao  Category 3com.quickspender.android.data.local.dao.CategoryDao  Delete 3com.quickspender.android.data.local.dao.CategoryDao  Flow 3com.quickspender.android.data.local.dao.CategoryDao  Insert 3com.quickspender.android.data.local.dao.CategoryDao  Int 3com.quickspender.android.data.local.dao.CategoryDao  List 3com.quickspender.android.data.local.dao.CategoryDao  OnConflictStrategy 3com.quickspender.android.data.local.dao.CategoryDao  Query 3com.quickspender.android.data.local.dao.CategoryDao  String 3com.quickspender.android.data.local.dao.CategoryDao  Update 3com.quickspender.android.data.local.dao.CategoryDao  Delete 6com.quickspender.android.data.local.dao.TransactionDao  Flow 6com.quickspender.android.data.local.dao.TransactionDao  Insert 6com.quickspender.android.data.local.dao.TransactionDao  Int 6com.quickspender.android.data.local.dao.TransactionDao  List 6com.quickspender.android.data.local.dao.TransactionDao  OnConflictStrategy 6com.quickspender.android.data.local.dao.TransactionDao  Query 6com.quickspender.android.data.local.dao.TransactionDao  String 6com.quickspender.android.data.local.dao.TransactionDao  Transaction 6com.quickspender.android.data.local.dao.TransactionDao  Update 6com.quickspender.android.data.local.dao.TransactionDao  AuthenticationResponse #com.quickspender.android.data.model  Boolean #com.quickspender.android.data.model  Category #com.quickspender.android.data.model  CreateCategoryRequest #com.quickspender.android.data.model  CreateTransactionRequest #com.quickspender.android.data.model  Double #com.quickspender.android.data.model  Int #com.quickspender.android.data.model  LoginRequest #com.quickspender.android.data.model  RegisterRequest #com.quickspender.android.data.model  String #com.quickspender.android.data.model  SummaryResponse #com.quickspender.android.data.model  Transaction #com.quickspender.android.data.model  TransactionResponse #com.quickspender.android.data.model  UpdateCategoryRequest #com.quickspender.android.data.model  UpdateTransactionRequest #com.quickspender.android.data.model  Boolean ,com.quickspender.android.data.model.Category  Int ,com.quickspender.android.data.model.Category  
PrimaryKey ,com.quickspender.android.data.model.Category  String ,com.quickspender.android.data.model.Category  Double /com.quickspender.android.data.model.Transaction  Int /com.quickspender.android.data.model.Transaction  
PrimaryKey /com.quickspender.android.data.model.Transaction  String /com.quickspender.android.data.model.Transaction  Category ;com.quickspender.android.data.model.TransactionWithCategory  Embedded ;com.quickspender.android.data.model.TransactionWithCategory  Transaction ;com.quickspender.android.data.model.TransactionWithCategory  Boolean )com.quickspender.android.data.preferences  String )com.quickspender.android.data.preferences  UserPreferences )com.quickspender.android.data.preferences  	dataStore )com.quickspender.android.data.preferences  ApplicationContext 9com.quickspender.android.data.preferences.UserPreferences  AuthenticationResponse 9com.quickspender.android.data.preferences.UserPreferences  Boolean 9com.quickspender.android.data.preferences.UserPreferences  Context 9com.quickspender.android.data.preferences.UserPreferences  Flow 9com.quickspender.android.data.preferences.UserPreferences  Gson 9com.quickspender.android.data.preferences.UserPreferences  Inject 9com.quickspender.android.data.preferences.UserPreferences  String 9com.quickspender.android.data.preferences.UserPreferences  AuthRepository (com.quickspender.android.data.repository  Boolean (com.quickspender.android.data.repository  CategoryRepository (com.quickspender.android.data.repository  Double (com.quickspender.android.data.repository  Int (com.quickspender.android.data.repository  List (com.quickspender.android.data.repository  ReportRepository (com.quickspender.android.data.repository  Result (com.quickspender.android.data.repository  String (com.quickspender.android.data.repository  TransactionRepository (com.quickspender.android.data.repository  Unit (com.quickspender.android.data.repository  AuthApi 7com.quickspender.android.data.repository.AuthRepository  AuthenticationResponse 7com.quickspender.android.data.repository.AuthRepository  Boolean 7com.quickspender.android.data.repository.AuthRepository  Flow 7com.quickspender.android.data.repository.AuthRepository  Inject 7com.quickspender.android.data.repository.AuthRepository  Result 7com.quickspender.android.data.repository.AuthRepository  String 7com.quickspender.android.data.repository.AuthRepository  UserPreferences 7com.quickspender.android.data.repository.AuthRepository  currentUser 7com.quickspender.android.data.repository.AuthRepository  
isLoggedIn 7com.quickspender.android.data.repository.AuthRepository  Category ;com.quickspender.android.data.repository.CategoryRepository  CategoryApi ;com.quickspender.android.data.repository.CategoryRepository  CategoryDao ;com.quickspender.android.data.repository.CategoryRepository  Flow ;com.quickspender.android.data.repository.CategoryRepository  Inject ;com.quickspender.android.data.repository.CategoryRepository  Int ;com.quickspender.android.data.repository.CategoryRepository  List ;com.quickspender.android.data.repository.CategoryRepository  Result ;com.quickspender.android.data.repository.CategoryRepository  String ;com.quickspender.android.data.repository.CategoryRepository  Unit ;com.quickspender.android.data.repository.CategoryRepository  Inject 9com.quickspender.android.data.repository.ReportRepository  	ReportApi 9com.quickspender.android.data.repository.ReportRepository  Result 9com.quickspender.android.data.repository.ReportRepository  String 9com.quickspender.android.data.repository.ReportRepository  SummaryResponse 9com.quickspender.android.data.repository.ReportRepository  Double >com.quickspender.android.data.repository.TransactionRepository  Flow >com.quickspender.android.data.repository.TransactionRepository  Inject >com.quickspender.android.data.repository.TransactionRepository  Int >com.quickspender.android.data.repository.TransactionRepository  List >com.quickspender.android.data.repository.TransactionRepository  Result >com.quickspender.android.data.repository.TransactionRepository  String >com.quickspender.android.data.repository.TransactionRepository  Transaction >com.quickspender.android.data.repository.TransactionRepository  TransactionApi >com.quickspender.android.data.repository.TransactionRepository  TransactionDao >com.quickspender.android.data.repository.TransactionRepository  TransactionResponse >com.quickspender.android.data.repository.TransactionRepository  Unit >com.quickspender.android.data.repository.TransactionRepository  DatabaseModule com.quickspender.android.di  
NetworkModule com.quickspender.android.di  SingletonComponent com.quickspender.android.di  ApplicationContext *com.quickspender.android.di.DatabaseModule  CategoryDao *com.quickspender.android.di.DatabaseModule  Context *com.quickspender.android.di.DatabaseModule  Provides *com.quickspender.android.di.DatabaseModule  QuickSpenderDatabase *com.quickspender.android.di.DatabaseModule  	Singleton *com.quickspender.android.di.DatabaseModule  TransactionDao *com.quickspender.android.di.DatabaseModule  ApplicationContext )com.quickspender.android.di.NetworkModule  AuthApi )com.quickspender.android.di.NetworkModule  CategoryApi )com.quickspender.android.di.NetworkModule  Context )com.quickspender.android.di.NetworkModule  Gson )com.quickspender.android.di.NetworkModule  Interceptor )com.quickspender.android.di.NetworkModule  OkHttpClient )com.quickspender.android.di.NetworkModule  Provides )com.quickspender.android.di.NetworkModule  	ReportApi )com.quickspender.android.di.NetworkModule  Retrofit )com.quickspender.android.di.NetworkModule  	Singleton )com.quickspender.android.di.NetworkModule  TransactionApi )com.quickspender.android.di.NetworkModule  UserPreferences )com.quickspender.android.di.NetworkModule  AuthUiState (com.quickspender.android.ui.screens.auth  
AuthViewModel (com.quickspender.android.ui.screens.auth  String (com.quickspender.android.ui.screens.auth  AuthRepository 6com.quickspender.android.ui.screens.auth.AuthViewModel  AuthUiState 6com.quickspender.android.ui.screens.auth.AuthViewModel  Inject 6com.quickspender.android.ui.screens.auth.AuthViewModel  	StateFlow 6com.quickspender.android.ui.screens.auth.AuthViewModel  String 6com.quickspender.android.ui.screens.auth.AuthViewModel  authRepository 6com.quickspender.android.ui.screens.auth.AuthViewModel  CategoryManagementUiState .com.quickspender.android.ui.screens.categories  CategoryManagementViewModel .com.quickspender.android.ui.screens.categories  Int .com.quickspender.android.ui.screens.categories  String .com.quickspender.android.ui.screens.categories  CategoryManagementUiState Jcom.quickspender.android.ui.screens.categories.CategoryManagementViewModel  CategoryRepository Jcom.quickspender.android.ui.screens.categories.CategoryManagementViewModel  Inject Jcom.quickspender.android.ui.screens.categories.CategoryManagementViewModel  Int Jcom.quickspender.android.ui.screens.categories.CategoryManagementViewModel  	StateFlow Jcom.quickspender.android.ui.screens.categories.CategoryManagementViewModel  String Jcom.quickspender.android.ui.screens.categories.CategoryManagementViewModel  DashboardUiState -com.quickspender.android.ui.screens.dashboard  DashboardViewModel -com.quickspender.android.ui.screens.dashboard  DashboardUiState @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  Inject @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  ReportRepository @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  	StateFlow @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  TransactionRepository @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  AddTransactionUiState 0com.quickspender.android.ui.screens.transactions  AddTransactionViewModel 0com.quickspender.android.ui.screens.transactions  Double 0com.quickspender.android.ui.screens.transactions  Int 0com.quickspender.android.ui.screens.transactions  String 0com.quickspender.android.ui.screens.transactions  TransactionListUiState 0com.quickspender.android.ui.screens.transactions  TransactionListViewModel 0com.quickspender.android.ui.screens.transactions  AddTransactionUiState Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  CategoryRepository Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  Double Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  Inject Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  Int Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  	StateFlow Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  String Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  TransactionRepository Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  Inject Icom.quickspender.android.ui.screens.transactions.TransactionListViewModel  	StateFlow Icom.quickspender.android.ui.screens.transactions.TransactionListViewModel  String Icom.quickspender.android.ui.screens.transactions.TransactionListViewModel  TransactionListUiState Icom.quickspender.android.ui.screens.transactions.TransactionListViewModel  TransactionRepository Icom.quickspender.android.ui.screens.transactions.TransactionListViewModel  BackgroundGray !com.quickspender.android.ui.theme  CategoryBlue !com.quickspender.android.ui.theme  DarkColorScheme !com.quickspender.android.ui.theme  
ExpenseRed !com.quickspender.android.ui.theme  IncomeGreen !com.quickspender.android.ui.theme  LightColorScheme !com.quickspender.android.ui.theme  Pink40 !com.quickspender.android.ui.theme  Pink80 !com.quickspender.android.ui.theme  Purple40 !com.quickspender.android.ui.theme  Purple80 !com.quickspender.android.ui.theme  PurpleGrey40 !com.quickspender.android.ui.theme  PurpleGrey80 !com.quickspender.android.ui.theme  
Typography !com.quickspender.android.ui.theme  Binds dagger  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  HiltViewModelMap &dagger.hilt.android.internal.lifecycle  KeySet 7dagger.hilt.android.internal.lifecycle.HiltViewModelMap  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  OriginatingElement dagger.hilt.codegen  SingletonComponent dagger.hilt.components  GeneratedEntryPoint dagger.hilt.internal  IntoMap dagger.multibindings  LazyClassKey dagger.multibindings  Category 	java.lang  OnConflictStrategy 	java.lang  SingletonComponent 	java.lang  Transaction 	java.lang  	Generated javax.annotation.processing  Inject javax.inject  	Singleton javax.inject  Array kotlin  Boolean kotlin  Category kotlin  Double kotlin  Int kotlin  OnConflictStrategy kotlin  Result kotlin  SingletonComponent kotlin  String kotlin  Transaction kotlin  Unit kotlin  Volatile kotlin  arrayOf kotlin  Category kotlin.annotation  OnConflictStrategy kotlin.annotation  Result kotlin.annotation  SingletonComponent kotlin.annotation  Transaction kotlin.annotation  Volatile kotlin.annotation  Category kotlin.collections  List kotlin.collections  OnConflictStrategy kotlin.collections  Result kotlin.collections  SingletonComponent kotlin.collections  Transaction kotlin.collections  Volatile kotlin.collections  Category kotlin.comparisons  OnConflictStrategy kotlin.comparisons  Result kotlin.comparisons  SingletonComponent kotlin.comparisons  Transaction kotlin.comparisons  Volatile kotlin.comparisons  Category 	kotlin.io  OnConflictStrategy 	kotlin.io  Result 	kotlin.io  SingletonComponent 	kotlin.io  Transaction 	kotlin.io  Volatile 	kotlin.io  Category 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  Result 
kotlin.jvm  SingletonComponent 
kotlin.jvm  Transaction 
kotlin.jvm  Volatile 
kotlin.jvm  Category 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  Result 
kotlin.ranges  SingletonComponent 
kotlin.ranges  Transaction 
kotlin.ranges  Volatile 
kotlin.ranges  KClass kotlin.reflect  Category kotlin.sequences  OnConflictStrategy kotlin.sequences  Result kotlin.sequences  SingletonComponent kotlin.sequences  Transaction kotlin.sequences  Volatile kotlin.sequences  Category kotlin.text  OnConflictStrategy kotlin.text  Result kotlin.text  SingletonComponent kotlin.text  Transaction kotlin.text  Volatile kotlin.text  Flow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  Serializable kotlinx.serialization  Interceptor okhttp3  OkHttpClient okhttp3  Response 	retrofit2  Retrofit 	retrofit2  Body retrofit2.http  Path retrofit2.http  Query retrofit2.http  Currency androidx.lifecycle.ViewModel  CurrencyRepository androidx.lifecycle.ViewModel  CurrencySelectionUiState androidx.lifecycle.ViewModel  Currency #com.quickspender.android.data.model  CurrencyRepository (com.quickspender.android.data.repository  Currency ;com.quickspender.android.data.repository.CurrencyRepository  Flow ;com.quickspender.android.data.repository.CurrencyRepository  Inject ;com.quickspender.android.data.repository.CurrencyRepository  List ;com.quickspender.android.data.repository.CurrencyRepository  UserPreferences ;com.quickspender.android.data.repository.CurrencyRepository  CurrencySelectionUiState ,com.quickspender.android.ui.screens.currency  CurrencySelectionViewModel ,com.quickspender.android.ui.screens.currency  Currency Gcom.quickspender.android.ui.screens.currency.CurrencySelectionViewModel  CurrencyRepository Gcom.quickspender.android.ui.screens.currency.CurrencySelectionViewModel  CurrencySelectionUiState Gcom.quickspender.android.ui.screens.currency.CurrencySelectionViewModel  Inject Gcom.quickspender.android.ui.screens.currency.CurrencySelectionViewModel  	StateFlow Gcom.quickspender.android.ui.screens.currency.CurrencySelectionViewModel  CurrencyRepository @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  CurrencyRepository Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  CurrencyRepository Icom.quickspender.android.ui.screens.transactions.TransactionListViewModel                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            