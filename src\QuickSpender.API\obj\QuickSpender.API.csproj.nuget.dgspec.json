{"format": 1, "restore": {"D:\\Startups\\QuickSpender\\src\\QuickSpender.API\\QuickSpender.API.csproj": {}}, "projects": {"D:\\Startups\\QuickSpender\\src\\QuickSpender.API\\QuickSpender.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Startups\\QuickSpender\\src\\QuickSpender.API\\QuickSpender.API.csproj", "projectName": "QuickSpender.API", "projectPath": "D:\\Startups\\QuickSpender\\src\\QuickSpender.API\\QuickSpender.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Startups\\QuickSpender\\src\\QuickSpender.API\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Startups\\QuickSpender\\src\\QuickSpender.Application\\QuickSpender.Application.csproj": {"projectPath": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Application\\QuickSpender.Application.csproj"}, "D:\\Startups\\QuickSpender\\src\\QuickSpender.Contracts\\QuickSpender.Contracts.csproj": {"projectPath": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Contracts\\QuickSpender.Contracts.csproj"}, "D:\\Startups\\QuickSpender\\src\\QuickSpender.Infrastructure\\QuickSpender.Infrastructure.csproj": {"projectPath": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Infrastructure\\QuickSpender.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"FluentValidation.AspNetCore": {"target": "Package", "version": "[11.3.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[8.0.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-x64", "version": "[8.0.8, 8.0.8]"}, {"name": "Microsoft.NETCore.App.Host.linux-x64", "version": "[8.0.8, 8.0.8]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-x64", "version": "[8.0.8, 8.0.8]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-x64": {"#import": []}}}, "D:\\Startups\\QuickSpender\\src\\QuickSpender.Application\\QuickSpender.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Application\\QuickSpender.Application.csproj", "projectName": "QuickSpender.Application", "projectPath": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Application\\QuickSpender.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Application\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Startups\\QuickSpender\\src\\QuickSpender.Contracts\\QuickSpender.Contracts.csproj": {"projectPath": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Contracts\\QuickSpender.Contracts.csproj"}, "D:\\Startups\\QuickSpender\\src\\QuickSpender.Domain\\QuickSpender.Domain.csproj": {"projectPath": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Domain\\QuickSpender.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[12.0.1, )"}, "AutoMapper.Extensions.Microsoft.DependencyInjection": {"target": "Package", "version": "[12.0.1, )"}, "FluentValidation": {"target": "Package", "version": "[11.8.1, )"}, "FluentValidation.DependencyInjectionExtensions": {"target": "Package", "version": "[11.8.1, )"}, "MediatR": {"target": "Package", "version": "[12.2.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-x64", "version": "[8.0.8, 8.0.8]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-x64", "version": "[8.0.8, 8.0.8]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-x64": {"#import": []}}}, "D:\\Startups\\QuickSpender\\src\\QuickSpender.Contracts\\QuickSpender.Contracts.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Contracts\\QuickSpender.Contracts.csproj", "projectName": "QuickSpender.Contracts", "projectPath": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Contracts\\QuickSpender.Contracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Contracts\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-x64", "version": "[8.0.8, 8.0.8]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-x64", "version": "[8.0.8, 8.0.8]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-x64": {"#import": []}}}, "D:\\Startups\\QuickSpender\\src\\QuickSpender.Domain\\QuickSpender.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Domain\\QuickSpender.Domain.csproj", "projectName": "QuickSpender.Domain", "projectPath": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Domain\\QuickSpender.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Domain\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-x64", "version": "[8.0.8, 8.0.8]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-x64", "version": "[8.0.8, 8.0.8]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-x64": {"#import": []}}}, "D:\\Startups\\QuickSpender\\src\\QuickSpender.Infrastructure\\QuickSpender.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Infrastructure\\QuickSpender.Infrastructure.csproj", "projectName": "QuickSpender.Infrastructure", "projectPath": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Infrastructure\\QuickSpender.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Infrastructure\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\Startups\\QuickSpender\\src\\QuickSpender.Application\\QuickSpender.Application.csproj": {"projectPath": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Application\\QuickSpender.Application.csproj"}, "D:\\Startups\\QuickSpender\\src\\QuickSpender.Domain\\QuickSpender.Domain.csproj": {"projectPath": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Domain\\QuickSpender.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.IdentityModel.Tokens": {"target": "Package", "version": "[8.0.2, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[8.0.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-x64", "version": "[8.0.8, 8.0.8]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-x64", "version": "[8.0.8, 8.0.8]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-x64": {"#import": []}}}}}