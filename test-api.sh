#!/bin/bash

# Test script for QuickSpender API endpoints

echo "🧪 Testing QuickSpender API endpoints..."

SERVER_IP="***************"

echo ""
echo "📍 Testing endpoints on $SERVER_IP"
echo "=================================="

# Test 1: Server status page
echo "1. Testing server status page..."
if curl -s "http://$SERVER_IP/" | grep -q "QuickSpender"; then
    echo "   ✅ Server status page is working"
else
    echo "   ❌ Server status page failed"
fi

# Test 2: Health check via nginx proxy
echo "2. Testing health check (nginx proxy)..."
if curl -f -s "http://$SERVER_IP/quickspender/health" > /dev/null 2>&1; then
    echo "   ✅ Health check via nginx proxy is working"
else
    echo "   ❌ Health check via nginx proxy failed"
fi

# Test 3: Direct API health check
echo "3. Testing direct API health check..."
if curl -f -s "http://$SERVER_IP:8080/health" > /dev/null 2>&1; then
    echo "   ✅ Direct API health check is working"
else
    echo "   ❌ Direct API health check failed"
fi

# Test 4: API endpoint via nginx
echo "4. Testing API endpoint via nginx..."
if curl -f -s "http://$SERVER_IP/quickspender/api/" > /dev/null 2>&1; then
    echo "   ✅ API endpoint via nginx is accessible"
else
    echo "   ❌ API endpoint via nginx failed"
fi

# Test 5: Direct API endpoint
echo "5. Testing direct API endpoint..."
if curl -f -s "http://$SERVER_IP/api/" > /dev/null 2>&1; then
    echo "   ✅ Direct API endpoint is accessible"
else
    echo "   ❌ Direct API endpoint failed"
fi

echo ""
echo "📱 Android App Configuration:"
echo "=================================="
echo "Base URL: http://$SERVER_IP/quickspender/"
echo "API URL:  http://$SERVER_IP/quickspender/api/"
echo ""

echo "🔗 Available URLs:"
echo "=================================="
echo "• Server Status:    http://$SERVER_IP/"
echo "• QuickSpender API: http://$SERVER_IP/quickspender/"
echo "• Health Check:     http://$SERVER_IP/quickspender/health"
echo "• Direct API:       http://$SERVER_IP/api/"
echo "• Direct Health:    http://$SERVER_IP:8080/health"
echo ""

echo "✅ API testing completed!"
