#!/bin/bash

# Complete database reset script for QuickSpender

echo "🔄 Resetting QuickSpender database completely..."

# Configuration
REMOTE_HOST="${REMOTE_HOST:-***************}"
REMOTE_USER="${REMOTE_USER:-<PERSON><PERSON><PERSON><PERSON>}"
SSH_KEY="${SSH_KEY:-C:/Users/<USER>/.ssh/id_rsa}"

# Prompt for sudo password if not set
if [ -z "$SUDO_PASSWORD" ]; then
    echo "🔐 Enter sudo password for $REMOTE_USER@$REMOTE_HOST:"
    read -s SUDO_PASSWORD
    export SUDO_PASSWORD
fi

echo "🔗 Connecting to server for complete database reset..."

ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" << 'EOF'
    echo "🛑 Stopping any running containers..."
    echo "$SUDO_PASSWORD" | sudo -S docker-compose down 2>/dev/null || true
    
    echo "🧹 Completely cleaning up existing database..."
    # Stop any connections to the database
    echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = 'quickspender';" 2>/dev/null || true
    
    # Drop everything
    echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -c "DROP DATABASE IF EXISTS quickspender;" 2>/dev/null
    echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -c "DROP USER IF EXISTS quickspender;" 2>/dev/null
    
    echo "🔐 Generating new secure password..."
    # Generate a truly random password
    NEW_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    echo "Generated password: $NEW_PASSWORD"
    
    echo "🗄️  Creating fresh database and user..."
    echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -c "CREATE DATABASE quickspender;"
    echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -c "CREATE USER quickspender WITH PASSWORD '$NEW_PASSWORD';"
    echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE quickspender TO quickspender;"
    echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -c "ALTER USER quickspender CREATEDB;"
    echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -c "GRANT ALL ON SCHEMA public TO quickspender;" quickspender
    
    echo "🔧 Configuring PostgreSQL for Docker access..."
    PG_VERSION=$(echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -tAc "SELECT version();" | grep -oP 'PostgreSQL \K[0-9]+')
    PG_CONF_FILE="/etc/postgresql/$PG_VERSION/main/postgresql.conf"
    PG_HBA_FILE="/etc/postgresql/$PG_VERSION/main/pg_hba.conf"
    
    # Enable listening on all addresses
    if [ -f "$PG_CONF_FILE" ]; then
        echo "$SUDO_PASSWORD" | sudo -S sed -i "s/#listen_addresses = 'localhost'/listen_addresses = '*'/" "$PG_CONF_FILE"
        echo "$SUDO_PASSWORD" | sudo -S sed -i "s/listen_addresses = 'localhost'/listen_addresses = '*'/" "$PG_CONF_FILE"
    fi
    
    # Add Docker access rules
    if [ -f "$PG_HBA_FILE" ]; then
        # Remove old rules
        echo "$SUDO_PASSWORD" | sudo -S sed -i '/quickspender/d' "$PG_HBA_FILE"
        
        # Add new rules
        echo "$SUDO_PASSWORD" | sudo -S sh -c "echo 'host    quickspender    quickspender    *********/8            md5' >> $PG_HBA_FILE"
        echo "$SUDO_PASSWORD" | sudo -S sh -c "echo 'host    quickspender    quickspender    0.0.0.0/0               md5' >> $PG_HBA_FILE"
    fi
    
    echo "🔄 Restarting PostgreSQL..."
    echo "$SUDO_PASSWORD" | sudo -S systemctl restart postgresql
    sleep 5
    
    echo "🧪 Testing new database connection..."
    export PGPASSWORD="$NEW_PASSWORD"
    if psql -h localhost -p 5432 -U quickspender -d quickspender -c "SELECT 1;" &>/dev/null; then
        echo "✅ Database connection successful!"
    else
        echo "❌ Database connection failed!"
        exit 1
    fi
    unset PGPASSWORD
    
    echo "📝 Updating .env file..."
    cd /home/<USER>/quickspender
    if [ -f .env ]; then
        # Update .env file
        sed -i "s/CONNECTION_STRING=.*/CONNECTION_STRING=Host=host.docker.internal;Port=5432;Database=quickspender;Username=quickspender;Password=$NEW_PASSWORD/" .env
        sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$NEW_PASSWORD/" .env
        sed -i 's/DB_HOST=.*/DB_HOST=host.docker.internal/' .env
        echo "✅ .env file updated"
    fi
    
    echo "🚀 Starting container with new configuration..."
    echo "$SUDO_PASSWORD" | sudo -S docker-compose up -d --build
    
    echo "⏳ Waiting for container to start..."
    sleep 20
    
    echo "📊 Container status:"
    echo "$SUDO_PASSWORD" | sudo -S docker-compose ps
    
    echo "📋 Container logs:"
    echo "$SUDO_PASSWORD" | sudo -S docker-compose logs --tail=15
    
    echo "🧪 Testing API..."
    sleep 5
    if curl -f http://localhost:8080/health 2>/dev/null; then
        echo "✅ API is working!"
        echo "🌐 Your API is available at:"
        echo "   • http://***************/quickspender/"
        echo "   • http://***************/quickspender/health"
    else
        echo "❌ API still not responding"
        echo "More logs:"
        echo "$SUDO_PASSWORD" | sudo -S docker-compose logs --tail=30
    fi
    
    echo ""
    echo "🔑 Database credentials:"
    echo "   Database: quickspender"
    echo "   Username: quickspender"
    echo "   Password: $NEW_PASSWORD"
    echo "   Host: host.docker.internal (from container)"
EOF

echo ""
echo "✅ Database reset completed!"
