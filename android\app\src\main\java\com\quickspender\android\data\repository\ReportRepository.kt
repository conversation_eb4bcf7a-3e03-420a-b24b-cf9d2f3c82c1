package com.quickspender.android.data.repository

import com.quickspender.android.data.api.ReportApi
import com.quickspender.android.data.model.SummaryResponse
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class ReportRepository @Inject constructor(
    private val reportApi: ReportApi
) {
    suspend fun getSummary(fromDate: String, toDate: String): Result<SummaryResponse> {
        return try {
            val response = reportApi.getSummary(fromDate, toDate)
            if (response.isSuccessful && response.body() != null) {
                Result.success(response.body()!!)
            } else {
                Result.failure(Exception("Failed to fetch summary: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
