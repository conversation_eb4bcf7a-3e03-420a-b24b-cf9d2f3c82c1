  Activity android.app  Application android.app  Modifier android.app.Activity  QuickSpenderNavigation android.app.Activity  QuickSpenderTheme android.app.Activity  Scaffold android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  onCreate android.app.Activity  padding android.app.Activity  
setContent android.app.Activity  window android.app.Activity  Context android.content  Modifier android.content.Context  QuickSpenderNavigation android.content.Context  QuickSpenderTheme android.content.Context  Scaffold android.content.Context  applicationContext android.content.Context  	dataStore android.content.Context  enableEdgeToEdge android.content.Context  fillMaxSize android.content.Context  padding android.content.Context  
setContent android.content.Context  Modifier android.content.ContextWrapper  QuickSpenderNavigation android.content.ContextWrapper  QuickSpenderTheme android.content.ContextWrapper  Scaffold android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  padding android.content.ContextWrapper  
setContent android.content.ContextWrapper  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  View android.view  Modifier  android.view.ContextThemeWrapper  QuickSpenderNavigation  android.view.ContextThemeWrapper  QuickSpenderTheme  android.view.ContextThemeWrapper  Scaffold  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  context android.view.View  isInEditMode android.view.View  statusBarColor android.view.Window  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  	Companion #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  QuickSpenderNavigation #androidx.activity.ComponentActivity  QuickSpenderTheme #androidx.activity.ComponentActivity  Scaffold #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  Modifier -androidx.activity.ComponentActivity.Companion  QuickSpenderNavigation -androidx.activity.ComponentActivity.Companion  QuickSpenderTheme -androidx.activity.ComponentActivity.Companion  Scaffold -androidx.activity.ComponentActivity.Companion  enableEdgeToEdge -androidx.activity.ComponentActivity.Companion  fillMaxSize -androidx.activity.ComponentActivity.Companion  padding -androidx.activity.ComponentActivity.Companion  
setContent -androidx.activity.ComponentActivity.Companion  
setContent androidx.activity.compose  AnimatedContentScope androidx.compose.animation  AddTransactionScreen /androidx.compose.animation.AnimatedContentScope  
BottomNavItem /androidx.compose.animation.AnimatedContentScope  DashboardScreen /androidx.compose.animation.AnimatedContentScope  LoginScreen /androidx.compose.animation.AnimatedContentScope  
MainScreen /androidx.compose.animation.AnimatedContentScope  
ProfileScreen /androidx.compose.animation.AnimatedContentScope  RegisterScreen /androidx.compose.animation.AnimatedContentScope  TransactionListScreen /androidx.compose.animation.AnimatedContentScope  findStartDestination /androidx.compose.animation.AnimatedContentScope  
background androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  AddTransactionViewModel "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  
AuthViewModel "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  DashboardViewModel "androidx.compose.foundation.layout  DateTimeFormatter "androidx.compose.foundation.layout  Double "androidx.compose.foundation.layout  DropdownMenuItem "androidx.compose.foundation.layout  	Exception "androidx.compose.foundation.layout  
ExpenseRed "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  ExposedDropdownMenuBox "androidx.compose.foundation.layout  ExposedDropdownMenuDefaults "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  IncomeGreen "androidx.compose.foundation.layout  KeyboardOptions "androidx.compose.foundation.layout  KeyboardType "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  
LocalDateTime "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  PasswordStrength "androidx.compose.foundation.layout  PasswordStrengthIndicator "androidx.compose.foundation.layout  PasswordValidator "androidx.compose.foundation.layout  PasswordVisualTransformation "androidx.compose.foundation.layout  RadioButton "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  SummaryCard "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  TrailingIcon "androidx.compose.foundation.layout  TransactionItem "androidx.compose.foundation.layout  TransactionListViewModel "androidx.compose.foundation.layout  TransactionResponse "androidx.compose.foundation.layout  TransactionType "androidx.compose.foundation.layout  Triple "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  
fillMaxHeight "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  find "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  format "androidx.compose.foundation.layout  
formatDate "androidx.compose.foundation.layout  getPasswordStrength "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  ifBlank "androidx.compose.foundation.layout  isEmpty "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  
selectable "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  toDoubleOrNull "androidx.compose.foundation.layout  validatePassword "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  Box +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  clip +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  
fillMaxHeight +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  DropdownMenuItem .androidx.compose.foundation.layout.ColumnScope  
ExpenseRed .androidx.compose.foundation.layout.ColumnScope  ExposedDropdownMenuBox .androidx.compose.foundation.layout.ColumnScope  ExposedDropdownMenuDefaults .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  IncomeGreen .androidx.compose.foundation.layout.ColumnScope  KeyboardOptions .androidx.compose.foundation.layout.ColumnScope  KeyboardType .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  PasswordStrengthIndicator .androidx.compose.foundation.layout.ColumnScope  PasswordValidator .androidx.compose.foundation.layout.ColumnScope  PasswordVisualTransformation .androidx.compose.foundation.layout.ColumnScope  RadioButton .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  String .androidx.compose.foundation.layout.ColumnScope  SummaryCard .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  TrailingIcon .androidx.compose.foundation.layout.ColumnScope  TransactionItem .androidx.compose.foundation.layout.ColumnScope  TransactionType .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  
fillMaxHeight .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  find .androidx.compose.foundation.layout.ColumnScope  format .androidx.compose.foundation.layout.ColumnScope  
formatDate .androidx.compose.foundation.layout.ColumnScope  getValue .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  ifBlank .androidx.compose.foundation.layout.ColumnScope  
isNotBlank .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  mutableStateOf .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  provideDelegate .androidx.compose.foundation.layout.ColumnScope  remember .androidx.compose.foundation.layout.ColumnScope  
selectable .androidx.compose.foundation.layout.ColumnScope  setValue .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  toDoubleOrNull .androidx.compose.foundation.layout.ColumnScope  validatePassword .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  	Alignment +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  
ExpenseRed +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  IncomeGreen +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  NavigationBarItem +androidx.compose.foundation.layout.RowScope  RadioButton +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  String +androidx.compose.foundation.layout.RowScope  SummaryCard +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  TransactionType +androidx.compose.foundation.layout.RowScope  any +androidx.compose.foundation.layout.RowScope  currentBackStackEntryAsState +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  findStartDestination +androidx.compose.foundation.layout.RowScope  format +androidx.compose.foundation.layout.RowScope  
formatDate +androidx.compose.foundation.layout.RowScope  getValue +androidx.compose.foundation.layout.RowScope  	hierarchy +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  provideDelegate +androidx.compose.foundation.layout.RowScope  
selectable +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  compose +androidx.compose.foundation.layout.androidx  ui 3androidx.compose.foundation.layout.androidx.compose  graphics 6androidx.compose.foundation.layout.androidx.compose.ui  Color ?androidx.compose.foundation.layout.androidx.compose.ui.graphics  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  TransactionItem .androidx.compose.foundation.lazy.LazyItemScope  TransactionItem .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  
selectable %androidx.compose.foundation.selection  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardOptions  androidx.compose.foundation.text  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  Home ,androidx.compose.material.icons.Icons.Filled  List ,androidx.compose.material.icons.Icons.Filled  Person ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  Home &androidx.compose.material.icons.filled  List &androidx.compose.material.icons.filled  Person &androidx.compose.material.icons.filled  AddTransactionScreen androidx.compose.material3  AddTransactionViewModel androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  
AuthViewModel androidx.compose.material3  
BottomNavItem androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  DashboardScreen androidx.compose.material3  DashboardViewModel androidx.compose.material3  DateTimeFormatter androidx.compose.material3  Double androidx.compose.material3  DropdownMenuItem androidx.compose.material3  	Exception androidx.compose.material3  
ExpenseRed androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  ExposedDropdownMenuBox androidx.compose.material3  ExposedDropdownMenuBoxScope androidx.compose.material3  ExposedDropdownMenuDefaults androidx.compose.material3  
FontWeight androidx.compose.material3  Icon androidx.compose.material3  Icons androidx.compose.material3  ImageVector androidx.compose.material3  IncomeGreen androidx.compose.material3  KeyboardOptions androidx.compose.material3  KeyboardType androidx.compose.material3  LaunchedEffect androidx.compose.material3  
LazyColumn androidx.compose.material3  
LocalDateTime androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  
NavigationBar androidx.compose.material3  NavigationBarItem androidx.compose.material3  OptIn androidx.compose.material3  OutlinedTextField androidx.compose.material3  PasswordStrengthIndicator androidx.compose.material3  PasswordValidator androidx.compose.material3  PasswordVisualTransformation androidx.compose.material3  
ProfileScreen androidx.compose.material3  RadioButton androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  SummaryCard androidx.compose.material3  Text androidx.compose.material3  
TextButton androidx.compose.material3  TrailingIcon androidx.compose.material3  TransactionItem androidx.compose.material3  TransactionListScreen androidx.compose.material3  TransactionListViewModel androidx.compose.material3  TransactionResponse androidx.compose.material3  TransactionType androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  androidx androidx.compose.material3  any androidx.compose.material3  buttonColors androidx.compose.material3  
cardColors androidx.compose.material3  collectAsState androidx.compose.material3  currentBackStackEntryAsState androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  find androidx.compose.material3  findStartDestination androidx.compose.material3  forEach androidx.compose.material3  format androidx.compose.material3  
formatDate androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  ifBlank androidx.compose.material3  
isNotBlank androidx.compose.material3  
isNotEmpty androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  
selectable androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  toDoubleOrNull androidx.compose.material3  validatePassword androidx.compose.material3  weight androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  error &androidx.compose.material3.ColorScheme  errorContainer &androidx.compose.material3.ColorScheme  onErrorContainer &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  surfaceVariant &androidx.compose.material3.ColorScheme  DropdownMenuItem 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ExposedDropdownMenu 6androidx.compose.material3.ExposedDropdownMenuBoxScope  ExposedDropdownMenuDefaults 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Modifier 6androidx.compose.material3.ExposedDropdownMenuBoxScope  OutlinedTextField 6androidx.compose.material3.ExposedDropdownMenuBoxScope  Text 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TrailingIcon 6androidx.compose.material3.ExposedDropdownMenuBoxScope  fillMaxWidth 6androidx.compose.material3.ExposedDropdownMenuBoxScope  
menuAnchor 6androidx.compose.material3.ExposedDropdownMenuBoxScope  TrailingIcon 6androidx.compose.material3.ExposedDropdownMenuDefaults  colorScheme (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  	bodyLarge %androidx.compose.material3.Typography  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  
headlineLarge %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  compose #androidx.compose.material3.androidx  ui +androidx.compose.material3.androidx.compose  graphics .androidx.compose.material3.androidx.compose.ui  Color 7androidx.compose.material3.androidx.compose.ui.graphics  AddTransactionScreen androidx.compose.runtime  AddTransactionViewModel androidx.compose.runtime  	Alignment androidx.compose.runtime  Anchor androidx.compose.runtime  Arrangement androidx.compose.runtime  
AuthViewModel androidx.compose.runtime  
BottomNavItem androidx.compose.runtime  Box androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  DashboardScreen androidx.compose.runtime  DashboardViewModel androidx.compose.runtime  Double androidx.compose.runtime  DropdownMenuItem androidx.compose.runtime  
ExpenseRed androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  ExposedDropdownMenuBox androidx.compose.runtime  ExposedDropdownMenuDefaults androidx.compose.runtime  
FontWeight androidx.compose.runtime  Icon androidx.compose.runtime  Icons androidx.compose.runtime  ImageVector androidx.compose.runtime  IncomeGreen androidx.compose.runtime  KeyboardOptions androidx.compose.runtime  KeyboardType androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
LazyColumn androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  
NavigationBar androidx.compose.runtime  NavigationBarItem androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  PasswordStrengthIndicator androidx.compose.runtime  PasswordValidator androidx.compose.runtime  PasswordVisualTransformation androidx.compose.runtime  
ProfileScreen androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  RadioButton androidx.compose.runtime  Row androidx.compose.runtime  Scaffold androidx.compose.runtime  
SideEffect androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  SummaryCard androidx.compose.runtime  Text androidx.compose.runtime  
TextButton androidx.compose.runtime  TrailingIcon androidx.compose.runtime  TransactionItem androidx.compose.runtime  TransactionListScreen androidx.compose.runtime  TransactionListViewModel androidx.compose.runtime  TransactionType androidx.compose.runtime  Unit androidx.compose.runtime  androidx androidx.compose.runtime  any androidx.compose.runtime  buttonColors androidx.compose.runtime  
cardColors androidx.compose.runtime  collectAsState androidx.compose.runtime  currentBackStackEntryAsState androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  find androidx.compose.runtime  findStartDestination androidx.compose.runtime  forEach androidx.compose.runtime  format androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  ifBlank androidx.compose.runtime  
isNotBlank androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  
selectable androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  toDoubleOrNull androidx.compose.runtime  validatePassword androidx.compose.runtime  weight androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  compose !androidx.compose.runtime.androidx  ui )androidx.compose.runtime.androidx.compose  graphics ,androidx.compose.runtime.androidx.compose.ui  Color 5androidx.compose.runtime.androidx.compose.ui.graphics  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  
fillMaxHeight androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  
menuAnchor androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  
selectable androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  
selectable &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Color androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  copy "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  KeyboardType androidx.compose.ui.text.input  PasswordVisualTransformation androidx.compose.ui.text.input  	Companion +androidx.compose.ui.text.input.KeyboardType  Decimal +androidx.compose.ui.text.input.KeyboardType  Email +androidx.compose.ui.text.input.KeyboardType  Password +androidx.compose.ui.text.input.KeyboardType  Decimal 5androidx.compose.ui.text.input.KeyboardType.Companion  Email 5androidx.compose.ui.text.input.KeyboardType.Companion  Password 5androidx.compose.ui.text.input.KeyboardType.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  Bundle #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  QuickSpenderNavigation #androidx.core.app.ComponentActivity  QuickSpenderTheme #androidx.core.app.ComponentActivity  Scaffold #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  WindowCompat androidx.core.view  WindowInsetsControllerCompat androidx.core.view  getInsetsController androidx.core.view.WindowCompat  isAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  	DataStore androidx.datastore.core  data !androidx.datastore.core.DataStore  edit !androidx.datastore.core.DataStore  preferencesDataStore androidx.datastore.preferences  MutablePreferences #androidx.datastore.preferences.core  Preferences #androidx.datastore.preferences.core  booleanPreferencesKey #androidx.datastore.preferences.core  edit #androidx.datastore.preferences.core  stringPreferencesKey #androidx.datastore.preferences.core  clear 6androidx.datastore.preferences.core.MutablePreferences  set 6androidx.datastore.preferences.core.MutablePreferences  Key /androidx.datastore.preferences.core.Preferences  get /androidx.datastore.preferences.core.Preferences  
hiltViewModel  androidx.hilt.navigation.compose  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  NavBackStackEntry androidx.navigation  NavDestination androidx.navigation  NavGraph androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  NavOptionsBuilder androidx.navigation  PopUpToBuilder androidx.navigation  destination %androidx.navigation.NavBackStackEntry  graph !androidx.navigation.NavController  navigate !androidx.navigation.NavController  popBackStack !androidx.navigation.NavController  	hierarchy "androidx.navigation.NavDestination  id "androidx.navigation.NavDestination  route "androidx.navigation.NavDestination  	hierarchy ,androidx.navigation.NavDestination.Companion  findStartDestination androidx.navigation.NavGraph  findStartDestination &androidx.navigation.NavGraph.Companion  AddTransactionScreen #androidx.navigation.NavGraphBuilder  
BottomNavItem #androidx.navigation.NavGraphBuilder  DashboardScreen #androidx.navigation.NavGraphBuilder  LoginScreen #androidx.navigation.NavGraphBuilder  
MainScreen #androidx.navigation.NavGraphBuilder  
ProfileScreen #androidx.navigation.NavGraphBuilder  RegisterScreen #androidx.navigation.NavGraphBuilder  TransactionListScreen #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  findStartDestination #androidx.navigation.NavGraphBuilder  currentBackStackEntryAsState %androidx.navigation.NavHostController  graph %androidx.navigation.NavHostController  navigate %androidx.navigation.NavHostController  popBackStack %androidx.navigation.NavHostController  findStartDestination %androidx.navigation.NavOptionsBuilder  launchSingleTop %androidx.navigation.NavOptionsBuilder  popUpTo %androidx.navigation.NavOptionsBuilder  restoreState %androidx.navigation.NavOptionsBuilder  	inclusive "androidx.navigation.PopUpToBuilder  	saveState "androidx.navigation.PopUpToBuilder  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  currentBackStackEntryAsState androidx.navigation.compose  rememberNavController androidx.navigation.compose  Category 
androidx.room  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Embedded 
androidx.room  Entity 
androidx.room  Flow 
androidx.room  Insert 
androidx.room  Int 
androidx.room  List 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  String 
androidx.room  Transaction 
androidx.room  Update 
androidx.room  	Companion  androidx.room.OnConflictStrategy  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  databaseBuilder androidx.room.Room  Builder androidx.room.RoomDatabase  CategoryDao androidx.room.RoomDatabase  	Companion androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  QuickSpenderDatabase androidx.room.RoomDatabase  Room androidx.room.RoomDatabase  TransactionDao androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  databaseBuilder androidx.room.RoomDatabase  java androidx.room.RoomDatabase  synchronized androidx.room.RoomDatabase  build "androidx.room.RoomDatabase.Builder  QuickSpenderDatabase $androidx.room.RoomDatabase.Companion  Room $androidx.room.RoomDatabase.Companion  databaseBuilder $androidx.room.RoomDatabase.Companion  java $androidx.room.RoomDatabase.Companion  synchronized $androidx.room.RoomDatabase.Companion  Gson com.google.gson  GsonBuilder com.google.gson  fromJson com.google.gson.Gson  toJson com.google.gson.Gson  create com.google.gson.GsonBuilder  
setLenient com.google.gson.GsonBuilder  AndroidEntryPoint com.quickspender.android  Application com.quickspender.android  Bundle com.quickspender.android  ComponentActivity com.quickspender.android  HiltAndroidApp com.quickspender.android  MainActivity com.quickspender.android  Modifier com.quickspender.android  QuickSpenderApplication com.quickspender.android  QuickSpenderNavigation com.quickspender.android  QuickSpenderTheme com.quickspender.android  Scaffold com.quickspender.android  fillMaxSize com.quickspender.android  padding com.quickspender.android  Modifier %com.quickspender.android.MainActivity  QuickSpenderNavigation %com.quickspender.android.MainActivity  QuickSpenderTheme %com.quickspender.android.MainActivity  Scaffold %com.quickspender.android.MainActivity  enableEdgeToEdge %com.quickspender.android.MainActivity  fillMaxSize %com.quickspender.android.MainActivity  padding %com.quickspender.android.MainActivity  
setContent %com.quickspender.android.MainActivity  AuthApi !com.quickspender.android.data.api  AuthenticationResponse !com.quickspender.android.data.api  Body !com.quickspender.android.data.api  Category !com.quickspender.android.data.api  CategoryApi !com.quickspender.android.data.api  CreateCategoryRequest !com.quickspender.android.data.api  CreateTransactionRequest !com.quickspender.android.data.api  DELETE !com.quickspender.android.data.api  GET !com.quickspender.android.data.api  Int !com.quickspender.android.data.api  List !com.quickspender.android.data.api  LoginRequest !com.quickspender.android.data.api  POST !com.quickspender.android.data.api  PUT !com.quickspender.android.data.api  Path !com.quickspender.android.data.api  Query !com.quickspender.android.data.api  RegisterRequest !com.quickspender.android.data.api  	ReportApi !com.quickspender.android.data.api  Response !com.quickspender.android.data.api  String !com.quickspender.android.data.api  SummaryResponse !com.quickspender.android.data.api  TransactionApi !com.quickspender.android.data.api  TransactionResponse !com.quickspender.android.data.api  Unit !com.quickspender.android.data.api  UpdateCategoryRequest !com.quickspender.android.data.api  UpdateTransactionRequest !com.quickspender.android.data.api  login )com.quickspender.android.data.api.AuthApi  register )com.quickspender.android.data.api.AuthApi  createCategory -com.quickspender.android.data.api.CategoryApi  
getCategories -com.quickspender.android.data.api.CategoryApi  createTransaction 0com.quickspender.android.data.api.TransactionApi  getTransactions 0com.quickspender.android.data.api.TransactionApi  Category #com.quickspender.android.data.local  CategoryDao #com.quickspender.android.data.local  Context #com.quickspender.android.data.local  Database #com.quickspender.android.data.local  QuickSpenderDatabase #com.quickspender.android.data.local  Room #com.quickspender.android.data.local  RoomDatabase #com.quickspender.android.data.local  Transaction #com.quickspender.android.data.local  TransactionDao #com.quickspender.android.data.local  Volatile #com.quickspender.android.data.local  databaseBuilder #com.quickspender.android.data.local  java #com.quickspender.android.data.local  synchronized #com.quickspender.android.data.local  CategoryDao 8com.quickspender.android.data.local.QuickSpenderDatabase  	Companion 8com.quickspender.android.data.local.QuickSpenderDatabase  Context 8com.quickspender.android.data.local.QuickSpenderDatabase  INSTANCE 8com.quickspender.android.data.local.QuickSpenderDatabase  QuickSpenderDatabase 8com.quickspender.android.data.local.QuickSpenderDatabase  Room 8com.quickspender.android.data.local.QuickSpenderDatabase  TransactionDao 8com.quickspender.android.data.local.QuickSpenderDatabase  Volatile 8com.quickspender.android.data.local.QuickSpenderDatabase  categoryDao 8com.quickspender.android.data.local.QuickSpenderDatabase  databaseBuilder 8com.quickspender.android.data.local.QuickSpenderDatabase  getDatabase 8com.quickspender.android.data.local.QuickSpenderDatabase  java 8com.quickspender.android.data.local.QuickSpenderDatabase  synchronized 8com.quickspender.android.data.local.QuickSpenderDatabase  transactionDao 8com.quickspender.android.data.local.QuickSpenderDatabase  INSTANCE Bcom.quickspender.android.data.local.QuickSpenderDatabase.Companion  QuickSpenderDatabase Bcom.quickspender.android.data.local.QuickSpenderDatabase.Companion  Room Bcom.quickspender.android.data.local.QuickSpenderDatabase.Companion  databaseBuilder Bcom.quickspender.android.data.local.QuickSpenderDatabase.Companion  getDatabase Bcom.quickspender.android.data.local.QuickSpenderDatabase.Companion  java Bcom.quickspender.android.data.local.QuickSpenderDatabase.Companion  synchronized Bcom.quickspender.android.data.local.QuickSpenderDatabase.Companion  Category 'com.quickspender.android.data.local.dao  CategoryDao 'com.quickspender.android.data.local.dao  Dao 'com.quickspender.android.data.local.dao  Delete 'com.quickspender.android.data.local.dao  Flow 'com.quickspender.android.data.local.dao  Insert 'com.quickspender.android.data.local.dao  Int 'com.quickspender.android.data.local.dao  List 'com.quickspender.android.data.local.dao  OnConflictStrategy 'com.quickspender.android.data.local.dao  Query 'com.quickspender.android.data.local.dao  String 'com.quickspender.android.data.local.dao  Transaction 'com.quickspender.android.data.local.dao  TransactionDao 'com.quickspender.android.data.local.dao  Update 'com.quickspender.android.data.local.dao  OnConflictStrategy 3com.quickspender.android.data.local.dao.CategoryDao  getAllCategories 3com.quickspender.android.data.local.dao.CategoryDao  getCategoriesByType 3com.quickspender.android.data.local.dao.CategoryDao  insertCategories 3com.quickspender.android.data.local.dao.CategoryDao  insertCategory 3com.quickspender.android.data.local.dao.CategoryDao  OnConflictStrategy 6com.quickspender.android.data.local.dao.TransactionDao  getAllTransactions 6com.quickspender.android.data.local.dao.TransactionDao  insertTransaction 6com.quickspender.android.data.local.dao.TransactionDao  insertTransactions 6com.quickspender.android.data.local.dao.TransactionDao  AuthenticationResponse #com.quickspender.android.data.model  Boolean #com.quickspender.android.data.model  Category #com.quickspender.android.data.model  CategorySummaryResponse #com.quickspender.android.data.model  CategoryType #com.quickspender.android.data.model  CreateCategoryRequest #com.quickspender.android.data.model  CreateTransactionRequest #com.quickspender.android.data.model  Double #com.quickspender.android.data.model  EXPENSE #com.quickspender.android.data.model  Embedded #com.quickspender.android.data.model  Entity #com.quickspender.android.data.model  Int #com.quickspender.android.data.model  List #com.quickspender.android.data.model  LoginRequest #com.quickspender.android.data.model  
PrimaryKey #com.quickspender.android.data.model  RegisterRequest #com.quickspender.android.data.model  Serializable #com.quickspender.android.data.model  String #com.quickspender.android.data.model  SummaryResponse #com.quickspender.android.data.model  Transaction #com.quickspender.android.data.model  TransactionResponse #com.quickspender.android.data.model  TransactionType #com.quickspender.android.data.model  TransactionWithCategory #com.quickspender.android.data.model  UpdateCategoryRequest #com.quickspender.android.data.model  UpdateTransactionRequest #com.quickspender.android.data.model  User #com.quickspender.android.data.model  entries #com.quickspender.android.data.model  find #com.quickspender.android.data.model  	Companion :com.quickspender.android.data.model.AuthenticationResponse  String :com.quickspender.android.data.model.AuthenticationResponse  email :com.quickspender.android.data.model.AuthenticationResponse  	firstName :com.quickspender.android.data.model.AuthenticationResponse  lastName :com.quickspender.android.data.model.AuthenticationResponse  let :com.quickspender.android.data.model.AuthenticationResponse  token :com.quickspender.android.data.model.AuthenticationResponse  Boolean ,com.quickspender.android.data.model.Category  	Companion ,com.quickspender.android.data.model.Category  Int ,com.quickspender.android.data.model.Category  
PrimaryKey ,com.quickspender.android.data.model.Category  String ,com.quickspender.android.data.model.Category  id ,com.quickspender.android.data.model.Category  name ,com.quickspender.android.data.model.Category  Category ;com.quickspender.android.data.model.CategorySummaryResponse  Double ;com.quickspender.android.data.model.CategorySummaryResponse  Int ;com.quickspender.android.data.model.CategorySummaryResponse  CategoryType 0com.quickspender.android.data.model.CategoryType  EXPENSE 0com.quickspender.android.data.model.CategoryType  Int 0com.quickspender.android.data.model.CategoryType  entries 0com.quickspender.android.data.model.CategoryType  find 0com.quickspender.android.data.model.CategoryType  value 0com.quickspender.android.data.model.CategoryType  EXPENSE :com.quickspender.android.data.model.CategoryType.Companion  entries :com.quickspender.android.data.model.CategoryType.Companion  find :com.quickspender.android.data.model.CategoryType.Companion  Int 9com.quickspender.android.data.model.CreateCategoryRequest  String 9com.quickspender.android.data.model.CreateCategoryRequest  Double <com.quickspender.android.data.model.CreateTransactionRequest  Int <com.quickspender.android.data.model.CreateTransactionRequest  String <com.quickspender.android.data.model.CreateTransactionRequest  String 0com.quickspender.android.data.model.LoginRequest  String 3com.quickspender.android.data.model.RegisterRequest  CategorySummaryResponse 3com.quickspender.android.data.model.SummaryResponse  Double 3com.quickspender.android.data.model.SummaryResponse  List 3com.quickspender.android.data.model.SummaryResponse  String 3com.quickspender.android.data.model.SummaryResponse  	Companion /com.quickspender.android.data.model.Transaction  Double /com.quickspender.android.data.model.Transaction  Int /com.quickspender.android.data.model.Transaction  
PrimaryKey /com.quickspender.android.data.model.Transaction  String /com.quickspender.android.data.model.Transaction  Category 7com.quickspender.android.data.model.TransactionResponse  Double 7com.quickspender.android.data.model.TransactionResponse  Int 7com.quickspender.android.data.model.TransactionResponse  String 7com.quickspender.android.data.model.TransactionResponse  amount 7com.quickspender.android.data.model.TransactionResponse  category 7com.quickspender.android.data.model.TransactionResponse  	createdAt 7com.quickspender.android.data.model.TransactionResponse  description 7com.quickspender.android.data.model.TransactionResponse  id 7com.quickspender.android.data.model.TransactionResponse  notes 7com.quickspender.android.data.model.TransactionResponse  transactionDate 7com.quickspender.android.data.model.TransactionResponse  type 7com.quickspender.android.data.model.TransactionResponse  	Companion 3com.quickspender.android.data.model.TransactionType  EXPENSE 3com.quickspender.android.data.model.TransactionType  Int 3com.quickspender.android.data.model.TransactionType  TransactionType 3com.quickspender.android.data.model.TransactionType  entries 3com.quickspender.android.data.model.TransactionType  find 3com.quickspender.android.data.model.TransactionType  value 3com.quickspender.android.data.model.TransactionType  EXPENSE =com.quickspender.android.data.model.TransactionType.Companion  entries =com.quickspender.android.data.model.TransactionType.Companion  find =com.quickspender.android.data.model.TransactionType.Companion  Int 9com.quickspender.android.data.model.UpdateCategoryRequest  String 9com.quickspender.android.data.model.UpdateCategoryRequest  Double <com.quickspender.android.data.model.UpdateTransactionRequest  Int <com.quickspender.android.data.model.UpdateTransactionRequest  String <com.quickspender.android.data.model.UpdateTransactionRequest  String (com.quickspender.android.data.model.User  ApplicationContext )com.quickspender.android.data.preferences  AuthenticationResponse )com.quickspender.android.data.preferences  Boolean )com.quickspender.android.data.preferences  Context )com.quickspender.android.data.preferences  	DataStore )com.quickspender.android.data.preferences  	Exception )com.quickspender.android.data.preferences  Flow )com.quickspender.android.data.preferences  Gson )com.quickspender.android.data.preferences  Inject )com.quickspender.android.data.preferences  Preferences )com.quickspender.android.data.preferences  PreferencesKeys )com.quickspender.android.data.preferences  	Singleton )com.quickspender.android.data.preferences  String )com.quickspender.android.data.preferences  UserPreferences )com.quickspender.android.data.preferences  booleanPreferencesKey )com.quickspender.android.data.preferences  	dataStore )com.quickspender.android.data.preferences  edit )com.quickspender.android.data.preferences  java )com.quickspender.android.data.preferences  let )com.quickspender.android.data.preferences  map )com.quickspender.android.data.preferences  provideDelegate )com.quickspender.android.data.preferences  stringPreferencesKey )com.quickspender.android.data.preferences  ApplicationContext 9com.quickspender.android.data.preferences.UserPreferences  AuthenticationResponse 9com.quickspender.android.data.preferences.UserPreferences  Boolean 9com.quickspender.android.data.preferences.UserPreferences  Context 9com.quickspender.android.data.preferences.UserPreferences  	Exception 9com.quickspender.android.data.preferences.UserPreferences  Flow 9com.quickspender.android.data.preferences.UserPreferences  Gson 9com.quickspender.android.data.preferences.UserPreferences  Inject 9com.quickspender.android.data.preferences.UserPreferences  PreferencesKeys 9com.quickspender.android.data.preferences.UserPreferences  String 9com.quickspender.android.data.preferences.UserPreferences  	authToken 9com.quickspender.android.data.preferences.UserPreferences  booleanPreferencesKey 9com.quickspender.android.data.preferences.UserPreferences  
clearAuthData 9com.quickspender.android.data.preferences.UserPreferences  context 9com.quickspender.android.data.preferences.UserPreferences  currentUser 9com.quickspender.android.data.preferences.UserPreferences  	dataStore 9com.quickspender.android.data.preferences.UserPreferences  edit 9com.quickspender.android.data.preferences.UserPreferences  gson 9com.quickspender.android.data.preferences.UserPreferences  
isLoggedIn 9com.quickspender.android.data.preferences.UserPreferences  java 9com.quickspender.android.data.preferences.UserPreferences  let 9com.quickspender.android.data.preferences.UserPreferences  map 9com.quickspender.android.data.preferences.UserPreferences  saveAuthData 9com.quickspender.android.data.preferences.UserPreferences  stringPreferencesKey 9com.quickspender.android.data.preferences.UserPreferences  
AUTH_TOKEN Icom.quickspender.android.data.preferences.UserPreferences.PreferencesKeys  CURRENT_USER Icom.quickspender.android.data.preferences.UserPreferences.PreferencesKeys  IS_LOGGED_IN Icom.quickspender.android.data.preferences.UserPreferences.PreferencesKeys  booleanPreferencesKey Icom.quickspender.android.data.preferences.UserPreferences.PreferencesKeys  stringPreferencesKey Icom.quickspender.android.data.preferences.UserPreferences.PreferencesKeys  AuthApi (com.quickspender.android.data.repository  AuthRepository (com.quickspender.android.data.repository  AuthenticationResponse (com.quickspender.android.data.repository  Boolean (com.quickspender.android.data.repository  Category (com.quickspender.android.data.repository  CategoryApi (com.quickspender.android.data.repository  CategoryDao (com.quickspender.android.data.repository  CategoryRepository (com.quickspender.android.data.repository  CreateCategoryRequest (com.quickspender.android.data.repository  CreateTransactionRequest (com.quickspender.android.data.repository  Double (com.quickspender.android.data.repository  	Exception (com.quickspender.android.data.repository  Flow (com.quickspender.android.data.repository  Inject (com.quickspender.android.data.repository  Int (com.quickspender.android.data.repository  List (com.quickspender.android.data.repository  LoginRequest (com.quickspender.android.data.repository  RegisterRequest (com.quickspender.android.data.repository  Result (com.quickspender.android.data.repository  	Singleton (com.quickspender.android.data.repository  String (com.quickspender.android.data.repository  Transaction (com.quickspender.android.data.repository  TransactionApi (com.quickspender.android.data.repository  TransactionDao (com.quickspender.android.data.repository  TransactionRepository (com.quickspender.android.data.repository  TransactionResponse (com.quickspender.android.data.repository  UserPreferences (com.quickspender.android.data.repository  	emptyList (com.quickspender.android.data.repository  failure (com.quickspender.android.data.repository  map (com.quickspender.android.data.repository  success (com.quickspender.android.data.repository  	Exception 7com.quickspender.android.data.repository.AuthRepository  LoginRequest 7com.quickspender.android.data.repository.AuthRepository  RegisterRequest 7com.quickspender.android.data.repository.AuthRepository  Result 7com.quickspender.android.data.repository.AuthRepository  authApi 7com.quickspender.android.data.repository.AuthRepository  currentUser 7com.quickspender.android.data.repository.AuthRepository  failure 7com.quickspender.android.data.repository.AuthRepository  
isLoggedIn 7com.quickspender.android.data.repository.AuthRepository  login 7com.quickspender.android.data.repository.AuthRepository  logout 7com.quickspender.android.data.repository.AuthRepository  register 7com.quickspender.android.data.repository.AuthRepository  success 7com.quickspender.android.data.repository.AuthRepository  userPreferences 7com.quickspender.android.data.repository.AuthRepository  CreateCategoryRequest ;com.quickspender.android.data.repository.CategoryRepository  	Exception ;com.quickspender.android.data.repository.CategoryRepository  Result ;com.quickspender.android.data.repository.CategoryRepository  categoryApi ;com.quickspender.android.data.repository.CategoryRepository  categoryDao ;com.quickspender.android.data.repository.CategoryRepository  	emptyList ;com.quickspender.android.data.repository.CategoryRepository  failure ;com.quickspender.android.data.repository.CategoryRepository  
getCategories ;com.quickspender.android.data.repository.CategoryRepository  success ;com.quickspender.android.data.repository.CategoryRepository  CreateTransactionRequest >com.quickspender.android.data.repository.TransactionRepository  	Exception >com.quickspender.android.data.repository.TransactionRepository  Result >com.quickspender.android.data.repository.TransactionRepository  Transaction >com.quickspender.android.data.repository.TransactionRepository  createTransaction >com.quickspender.android.data.repository.TransactionRepository  	emptyList >com.quickspender.android.data.repository.TransactionRepository  failure >com.quickspender.android.data.repository.TransactionRepository  getTransactions >com.quickspender.android.data.repository.TransactionRepository  map >com.quickspender.android.data.repository.TransactionRepository  success >com.quickspender.android.data.repository.TransactionRepository  transactionApi >com.quickspender.android.data.repository.TransactionRepository  transactionDao >com.quickspender.android.data.repository.TransactionRepository  ApplicationContext com.quickspender.android.di  Array com.quickspender.android.di  AuthApi com.quickspender.android.di  CategoryApi com.quickspender.android.di  CategoryDao com.quickspender.android.di  Context com.quickspender.android.di  DatabaseModule com.quickspender.android.di  Gson com.quickspender.android.di  GsonBuilder com.quickspender.android.di  GsonConverterFactory com.quickspender.android.di  HttpLoggingInterceptor com.quickspender.android.di  	InstallIn com.quickspender.android.di  Interceptor com.quickspender.android.di  Module com.quickspender.android.di  
NetworkModule com.quickspender.android.di  OkHttpClient com.quickspender.android.di  Provides com.quickspender.android.di  QuickSpenderDatabase com.quickspender.android.di  	ReportApi com.quickspender.android.di  Retrofit com.quickspender.android.di  
SSLContext com.quickspender.android.di  	Singleton com.quickspender.android.di  SingletonComponent com.quickspender.android.di  String com.quickspender.android.di  TransactionApi com.quickspender.android.di  TransactionDao com.quickspender.android.di  TrustManager com.quickspender.android.di  UserPreferences com.quickspender.android.di  X509Certificate com.quickspender.android.di  X509TrustManager com.quickspender.android.di  apply com.quickspender.android.di  arrayOf com.quickspender.android.di  first com.quickspender.android.di  getDatabase com.quickspender.android.di  java com.quickspender.android.di  let com.quickspender.android.di  runBlocking com.quickspender.android.di  QuickSpenderDatabase *com.quickspender.android.di.DatabaseModule  getDatabase *com.quickspender.android.di.DatabaseModule  AuthApi )com.quickspender.android.di.NetworkModule  BASE_URL )com.quickspender.android.di.NetworkModule  CategoryApi )com.quickspender.android.di.NetworkModule  GsonBuilder )com.quickspender.android.di.NetworkModule  GsonConverterFactory )com.quickspender.android.di.NetworkModule  HttpLoggingInterceptor )com.quickspender.android.di.NetworkModule  Interceptor )com.quickspender.android.di.NetworkModule  OkHttpClient )com.quickspender.android.di.NetworkModule  	ReportApi )com.quickspender.android.di.NetworkModule  Retrofit )com.quickspender.android.di.NetworkModule  
SSLContext )com.quickspender.android.di.NetworkModule  TransactionApi )com.quickspender.android.di.NetworkModule  apply )com.quickspender.android.di.NetworkModule  arrayOf )com.quickspender.android.di.NetworkModule  first )com.quickspender.android.di.NetworkModule  java )com.quickspender.android.di.NetworkModule  let )com.quickspender.android.di.NetworkModule  runBlocking )com.quickspender.android.di.NetworkModule  	Alignment &com.quickspender.android.ui.components  Arrangement &com.quickspender.android.ui.components  Box &com.quickspender.android.ui.components  Column &com.quickspender.android.ui.components  
Composable &com.quickspender.android.ui.components  
MaterialTheme &com.quickspender.android.ui.components  Modifier &com.quickspender.android.ui.components  PasswordStrength &com.quickspender.android.ui.components  PasswordStrengthIndicator &com.quickspender.android.ui.components  PasswordValidator &com.quickspender.android.ui.components  RoundedCornerShape &com.quickspender.android.ui.components  Row &com.quickspender.android.ui.components  Spacer &com.quickspender.android.ui.components  String &com.quickspender.android.ui.components  Text &com.quickspender.android.ui.components  Triple &com.quickspender.android.ui.components  
background &com.quickspender.android.ui.components  clip &com.quickspender.android.ui.components  
fillMaxHeight &com.quickspender.android.ui.components  fillMaxWidth &com.quickspender.android.ui.components  getPasswordStrength &com.quickspender.android.ui.components  height &com.quickspender.android.ui.components  isEmpty &com.quickspender.android.ui.components  
AuthViewModel &com.quickspender.android.ui.navigation  
Composable &com.quickspender.android.ui.navigation  LoginScreen &com.quickspender.android.ui.navigation  
MainScreen &com.quickspender.android.ui.navigation  Modifier &com.quickspender.android.ui.navigation  NavHostController &com.quickspender.android.ui.navigation  QuickSpenderNavigation &com.quickspender.android.ui.navigation  RegisterScreen &com.quickspender.android.ui.navigation  provideDelegate &com.quickspender.android.ui.navigation  	Alignment (com.quickspender.android.ui.screens.auth  Arrangement (com.quickspender.android.ui.screens.auth  AuthRepository (com.quickspender.android.ui.screens.auth  AuthUiState (com.quickspender.android.ui.screens.auth  
AuthViewModel (com.quickspender.android.ui.screens.auth  Boolean (com.quickspender.android.ui.screens.auth  Button (com.quickspender.android.ui.screens.auth  Card (com.quickspender.android.ui.screens.auth  CardDefaults (com.quickspender.android.ui.screens.auth  CircularProgressIndicator (com.quickspender.android.ui.screens.auth  Column (com.quickspender.android.ui.screens.auth  
Composable (com.quickspender.android.ui.screens.auth  
ExpenseRed (com.quickspender.android.ui.screens.auth  ExperimentalMaterial3Api (com.quickspender.android.ui.screens.auth  
HiltViewModel (com.quickspender.android.ui.screens.auth  IncomeGreen (com.quickspender.android.ui.screens.auth  Inject (com.quickspender.android.ui.screens.auth  KeyboardOptions (com.quickspender.android.ui.screens.auth  KeyboardType (com.quickspender.android.ui.screens.auth  LaunchedEffect (com.quickspender.android.ui.screens.auth  LoginScreen (com.quickspender.android.ui.screens.auth  
MaterialTheme (com.quickspender.android.ui.screens.auth  Modifier (com.quickspender.android.ui.screens.auth  MutableStateFlow (com.quickspender.android.ui.screens.auth  OptIn (com.quickspender.android.ui.screens.auth  OutlinedTextField (com.quickspender.android.ui.screens.auth  PasswordStrengthIndicator (com.quickspender.android.ui.screens.auth  PasswordValidator (com.quickspender.android.ui.screens.auth  PasswordVisualTransformation (com.quickspender.android.ui.screens.auth  RegisterScreen (com.quickspender.android.ui.screens.auth  Row (com.quickspender.android.ui.screens.auth  Spacer (com.quickspender.android.ui.screens.auth  	StateFlow (com.quickspender.android.ui.screens.auth  String (com.quickspender.android.ui.screens.auth  Text (com.quickspender.android.ui.screens.auth  
TextButton (com.quickspender.android.ui.screens.auth  Unit (com.quickspender.android.ui.screens.auth  	ViewModel (com.quickspender.android.ui.screens.auth  _uiState (com.quickspender.android.ui.screens.auth  asStateFlow (com.quickspender.android.ui.screens.auth  authRepository (com.quickspender.android.ui.screens.auth  
cardColors (com.quickspender.android.ui.screens.auth  collectAsState (com.quickspender.android.ui.screens.auth  fillMaxSize (com.quickspender.android.ui.screens.auth  fillMaxWidth (com.quickspender.android.ui.screens.auth  forEach (com.quickspender.android.ui.screens.auth  getValue (com.quickspender.android.ui.screens.auth  height (com.quickspender.android.ui.screens.auth  
isNotBlank (com.quickspender.android.ui.screens.auth  
isNotEmpty (com.quickspender.android.ui.screens.auth  launch (com.quickspender.android.ui.screens.auth  let (com.quickspender.android.ui.screens.auth  mutableStateOf (com.quickspender.android.ui.screens.auth  	onFailure (com.quickspender.android.ui.screens.auth  	onSuccess (com.quickspender.android.ui.screens.auth  padding (com.quickspender.android.ui.screens.auth  provideDelegate (com.quickspender.android.ui.screens.auth  remember (com.quickspender.android.ui.screens.auth  setValue (com.quickspender.android.ui.screens.auth  size (com.quickspender.android.ui.screens.auth  validatePassword (com.quickspender.android.ui.screens.auth  copy 4com.quickspender.android.ui.screens.auth.AuthUiState  error 4com.quickspender.android.ui.screens.auth.AuthUiState  	isLoading 4com.quickspender.android.ui.screens.auth.AuthUiState  loginSuccess 4com.quickspender.android.ui.screens.auth.AuthUiState  registerSuccess 4com.quickspender.android.ui.screens.auth.AuthUiState  AuthUiState 6com.quickspender.android.ui.screens.auth.AuthViewModel  MutableStateFlow 6com.quickspender.android.ui.screens.auth.AuthViewModel  _uiState 6com.quickspender.android.ui.screens.auth.AuthViewModel  asStateFlow 6com.quickspender.android.ui.screens.auth.AuthViewModel  authRepository 6com.quickspender.android.ui.screens.auth.AuthViewModel  clearSuccessStates 6com.quickspender.android.ui.screens.auth.AuthViewModel  currentUser 6com.quickspender.android.ui.screens.auth.AuthViewModel  
isLoggedIn 6com.quickspender.android.ui.screens.auth.AuthViewModel  launch 6com.quickspender.android.ui.screens.auth.AuthViewModel  login 6com.quickspender.android.ui.screens.auth.AuthViewModel  logout 6com.quickspender.android.ui.screens.auth.AuthViewModel  	onFailure 6com.quickspender.android.ui.screens.auth.AuthViewModel  	onSuccess 6com.quickspender.android.ui.screens.auth.AuthViewModel  register 6com.quickspender.android.ui.screens.auth.AuthViewModel  uiState 6com.quickspender.android.ui.screens.auth.AuthViewModel  viewModelScope 6com.quickspender.android.ui.screens.auth.AuthViewModel  	Alignment -com.quickspender.android.ui.screens.dashboard  Arrangement -com.quickspender.android.ui.screens.dashboard  Boolean -com.quickspender.android.ui.screens.dashboard  Box -com.quickspender.android.ui.screens.dashboard  Card -com.quickspender.android.ui.screens.dashboard  CardDefaults -com.quickspender.android.ui.screens.dashboard  CircularProgressIndicator -com.quickspender.android.ui.screens.dashboard  Column -com.quickspender.android.ui.screens.dashboard  
Composable -com.quickspender.android.ui.screens.dashboard  DashboardScreen -com.quickspender.android.ui.screens.dashboard  DashboardUiState -com.quickspender.android.ui.screens.dashboard  DashboardViewModel -com.quickspender.android.ui.screens.dashboard  DateTimeFormatter -com.quickspender.android.ui.screens.dashboard  Double -com.quickspender.android.ui.screens.dashboard  	Exception -com.quickspender.android.ui.screens.dashboard  
ExpenseRed -com.quickspender.android.ui.screens.dashboard  ExperimentalMaterial3Api -com.quickspender.android.ui.screens.dashboard  
FontWeight -com.quickspender.android.ui.screens.dashboard  
HiltViewModel -com.quickspender.android.ui.screens.dashboard  IncomeGreen -com.quickspender.android.ui.screens.dashboard  Inject -com.quickspender.android.ui.screens.dashboard  LaunchedEffect -com.quickspender.android.ui.screens.dashboard  
LazyColumn -com.quickspender.android.ui.screens.dashboard  List -com.quickspender.android.ui.screens.dashboard  	LocalDate -com.quickspender.android.ui.screens.dashboard  
LocalDateTime -com.quickspender.android.ui.screens.dashboard  
MaterialTheme -com.quickspender.android.ui.screens.dashboard  Modifier -com.quickspender.android.ui.screens.dashboard  MutableStateFlow -com.quickspender.android.ui.screens.dashboard  OptIn -com.quickspender.android.ui.screens.dashboard  Row -com.quickspender.android.ui.screens.dashboard  Spacer -com.quickspender.android.ui.screens.dashboard  	StateFlow -com.quickspender.android.ui.screens.dashboard  String -com.quickspender.android.ui.screens.dashboard  SummaryCard -com.quickspender.android.ui.screens.dashboard  Text -com.quickspender.android.ui.screens.dashboard  TransactionItem -com.quickspender.android.ui.screens.dashboard  TransactionRepository -com.quickspender.android.ui.screens.dashboard  TransactionResponse -com.quickspender.android.ui.screens.dashboard  Unit -com.quickspender.android.ui.screens.dashboard  	ViewModel -com.quickspender.android.ui.screens.dashboard  _uiState -com.quickspender.android.ui.screens.dashboard  androidx -com.quickspender.android.ui.screens.dashboard  asStateFlow -com.quickspender.android.ui.screens.dashboard  
cardColors -com.quickspender.android.ui.screens.dashboard  collectAsState -com.quickspender.android.ui.screens.dashboard  	emptyList -com.quickspender.android.ui.screens.dashboard  fillMaxSize -com.quickspender.android.ui.screens.dashboard  fillMaxWidth -com.quickspender.android.ui.screens.dashboard  filter -com.quickspender.android.ui.screens.dashboard  format -com.quickspender.android.ui.screens.dashboard  
formatDate -com.quickspender.android.ui.screens.dashboard  getValue -com.quickspender.android.ui.screens.dashboard  height -com.quickspender.android.ui.screens.dashboard  launch -com.quickspender.android.ui.screens.dashboard  let -com.quickspender.android.ui.screens.dashboard  padding -com.quickspender.android.ui.screens.dashboard  provideDelegate -com.quickspender.android.ui.screens.dashboard  spacedBy -com.quickspender.android.ui.screens.dashboard  sumOf -com.quickspender.android.ui.screens.dashboard  take -com.quickspender.android.ui.screens.dashboard  transactionRepository -com.quickspender.android.ui.screens.dashboard  weight -com.quickspender.android.ui.screens.dashboard  balance >com.quickspender.android.ui.screens.dashboard.DashboardUiState  copy >com.quickspender.android.ui.screens.dashboard.DashboardUiState  error >com.quickspender.android.ui.screens.dashboard.DashboardUiState  	isLoading >com.quickspender.android.ui.screens.dashboard.DashboardUiState  recentTransactions >com.quickspender.android.ui.screens.dashboard.DashboardUiState  
totalExpenses >com.quickspender.android.ui.screens.dashboard.DashboardUiState  totalIncome >com.quickspender.android.ui.screens.dashboard.DashboardUiState  DashboardUiState @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  DateTimeFormatter @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  	LocalDate @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  MutableStateFlow @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  _uiState @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  asStateFlow @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  filter @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  launch @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  loadDashboardData @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  sumOf @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  take @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  transactionRepository @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  uiState @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  viewModelScope @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  compose 6com.quickspender.android.ui.screens.dashboard.androidx  ui >com.quickspender.android.ui.screens.dashboard.androidx.compose  graphics Acom.quickspender.android.ui.screens.dashboard.androidx.compose.ui  Color Jcom.quickspender.android.ui.screens.dashboard.androidx.compose.ui.graphics  AddTransactionScreen (com.quickspender.android.ui.screens.main  
BottomNavItem (com.quickspender.android.ui.screens.main  
Composable (com.quickspender.android.ui.screens.main  DashboardScreen (com.quickspender.android.ui.screens.main  ExperimentalMaterial3Api (com.quickspender.android.ui.screens.main  Icon (com.quickspender.android.ui.screens.main  Icons (com.quickspender.android.ui.screens.main  ImageVector (com.quickspender.android.ui.screens.main  
MainScreen (com.quickspender.android.ui.screens.main  Modifier (com.quickspender.android.ui.screens.main  
NavigationBar (com.quickspender.android.ui.screens.main  NavigationBarItem (com.quickspender.android.ui.screens.main  OptIn (com.quickspender.android.ui.screens.main  
ProfileScreen (com.quickspender.android.ui.screens.main  Scaffold (com.quickspender.android.ui.screens.main  String (com.quickspender.android.ui.screens.main  Text (com.quickspender.android.ui.screens.main  TransactionListScreen (com.quickspender.android.ui.screens.main  Unit (com.quickspender.android.ui.screens.main  any (com.quickspender.android.ui.screens.main  currentBackStackEntryAsState (com.quickspender.android.ui.screens.main  findStartDestination (com.quickspender.android.ui.screens.main  forEach (com.quickspender.android.ui.screens.main  getValue (com.quickspender.android.ui.screens.main  listOf (com.quickspender.android.ui.screens.main  padding (com.quickspender.android.ui.screens.main  provideDelegate (com.quickspender.android.ui.screens.main  Add 6com.quickspender.android.ui.screens.main.BottomNavItem  AddTransaction 6com.quickspender.android.ui.screens.main.BottomNavItem  
BottomNavItem 6com.quickspender.android.ui.screens.main.BottomNavItem  	Dashboard 6com.quickspender.android.ui.screens.main.BottomNavItem  Home 6com.quickspender.android.ui.screens.main.BottomNavItem  Icons 6com.quickspender.android.ui.screens.main.BottomNavItem  ImageVector 6com.quickspender.android.ui.screens.main.BottomNavItem  List 6com.quickspender.android.ui.screens.main.BottomNavItem  Person 6com.quickspender.android.ui.screens.main.BottomNavItem  Profile 6com.quickspender.android.ui.screens.main.BottomNavItem  String 6com.quickspender.android.ui.screens.main.BottomNavItem  Transactions 6com.quickspender.android.ui.screens.main.BottomNavItem  icon 6com.quickspender.android.ui.screens.main.BottomNavItem  route 6com.quickspender.android.ui.screens.main.BottomNavItem  title 6com.quickspender.android.ui.screens.main.BottomNavItem  route Ecom.quickspender.android.ui.screens.main.BottomNavItem.AddTransaction  route @com.quickspender.android.ui.screens.main.BottomNavItem.Dashboard  route >com.quickspender.android.ui.screens.main.BottomNavItem.Profile  route Ccom.quickspender.android.ui.screens.main.BottomNavItem.Transactions  	Alignment +com.quickspender.android.ui.screens.profile  
AuthViewModel +com.quickspender.android.ui.screens.profile  Button +com.quickspender.android.ui.screens.profile  ButtonDefaults +com.quickspender.android.ui.screens.profile  Card +com.quickspender.android.ui.screens.profile  CardDefaults +com.quickspender.android.ui.screens.profile  Column +com.quickspender.android.ui.screens.profile  
Composable +com.quickspender.android.ui.screens.profile  
MaterialTheme +com.quickspender.android.ui.screens.profile  Modifier +com.quickspender.android.ui.screens.profile  
ProfileScreen +com.quickspender.android.ui.screens.profile  Spacer +com.quickspender.android.ui.screens.profile  Text +com.quickspender.android.ui.screens.profile  Unit +com.quickspender.android.ui.screens.profile  buttonColors +com.quickspender.android.ui.screens.profile  
cardColors +com.quickspender.android.ui.screens.profile  collectAsState +com.quickspender.android.ui.screens.profile  fillMaxSize +com.quickspender.android.ui.screens.profile  fillMaxWidth +com.quickspender.android.ui.screens.profile  getValue +com.quickspender.android.ui.screens.profile  height +com.quickspender.android.ui.screens.profile  let +com.quickspender.android.ui.screens.profile  padding +com.quickspender.android.ui.screens.profile  provideDelegate +com.quickspender.android.ui.screens.profile  AddTransactionScreen 0com.quickspender.android.ui.screens.transactions  AddTransactionUiState 0com.quickspender.android.ui.screens.transactions  AddTransactionViewModel 0com.quickspender.android.ui.screens.transactions  	Alignment 0com.quickspender.android.ui.screens.transactions  Arrangement 0com.quickspender.android.ui.screens.transactions  Boolean 0com.quickspender.android.ui.screens.transactions  Box 0com.quickspender.android.ui.screens.transactions  Button 0com.quickspender.android.ui.screens.transactions  Card 0com.quickspender.android.ui.screens.transactions  CardDefaults 0com.quickspender.android.ui.screens.transactions  Category 0com.quickspender.android.ui.screens.transactions  CategoryRepository 0com.quickspender.android.ui.screens.transactions  CircularProgressIndicator 0com.quickspender.android.ui.screens.transactions  Column 0com.quickspender.android.ui.screens.transactions  
Composable 0com.quickspender.android.ui.screens.transactions  DateTimeFormatter 0com.quickspender.android.ui.screens.transactions  Double 0com.quickspender.android.ui.screens.transactions  DropdownMenuItem 0com.quickspender.android.ui.screens.transactions  	Exception 0com.quickspender.android.ui.screens.transactions  ExperimentalMaterial3Api 0com.quickspender.android.ui.screens.transactions  ExposedDropdownMenuBox 0com.quickspender.android.ui.screens.transactions  ExposedDropdownMenuDefaults 0com.quickspender.android.ui.screens.transactions  
HiltViewModel 0com.quickspender.android.ui.screens.transactions  Inject 0com.quickspender.android.ui.screens.transactions  Int 0com.quickspender.android.ui.screens.transactions  KeyboardOptions 0com.quickspender.android.ui.screens.transactions  KeyboardType 0com.quickspender.android.ui.screens.transactions  LaunchedEffect 0com.quickspender.android.ui.screens.transactions  
LazyColumn 0com.quickspender.android.ui.screens.transactions  List 0com.quickspender.android.ui.screens.transactions  
LocalDateTime 0com.quickspender.android.ui.screens.transactions  
MaterialTheme 0com.quickspender.android.ui.screens.transactions  Modifier 0com.quickspender.android.ui.screens.transactions  MutableStateFlow 0com.quickspender.android.ui.screens.transactions  OptIn 0com.quickspender.android.ui.screens.transactions  OutlinedTextField 0com.quickspender.android.ui.screens.transactions  RadioButton 0com.quickspender.android.ui.screens.transactions  Row 0com.quickspender.android.ui.screens.transactions  Spacer 0com.quickspender.android.ui.screens.transactions  	StateFlow 0com.quickspender.android.ui.screens.transactions  String 0com.quickspender.android.ui.screens.transactions  Text 0com.quickspender.android.ui.screens.transactions  TrailingIcon 0com.quickspender.android.ui.screens.transactions  TransactionItem 0com.quickspender.android.ui.screens.transactions  TransactionListScreen 0com.quickspender.android.ui.screens.transactions  TransactionListUiState 0com.quickspender.android.ui.screens.transactions  TransactionListViewModel 0com.quickspender.android.ui.screens.transactions  TransactionRepository 0com.quickspender.android.ui.screens.transactions  TransactionResponse 0com.quickspender.android.ui.screens.transactions  TransactionType 0com.quickspender.android.ui.screens.transactions  Unit 0com.quickspender.android.ui.screens.transactions  	ViewModel 0com.quickspender.android.ui.screens.transactions  _uiState 0com.quickspender.android.ui.screens.transactions  asStateFlow 0com.quickspender.android.ui.screens.transactions  
cardColors 0com.quickspender.android.ui.screens.transactions  categoryRepository 0com.quickspender.android.ui.screens.transactions  collectAsState 0com.quickspender.android.ui.screens.transactions  	emptyList 0com.quickspender.android.ui.screens.transactions  fillMaxSize 0com.quickspender.android.ui.screens.transactions  fillMaxWidth 0com.quickspender.android.ui.screens.transactions  find 0com.quickspender.android.ui.screens.transactions  forEach 0com.quickspender.android.ui.screens.transactions  getValue 0com.quickspender.android.ui.screens.transactions  height 0com.quickspender.android.ui.screens.transactions  ifBlank 0com.quickspender.android.ui.screens.transactions  
isNotBlank 0com.quickspender.android.ui.screens.transactions  
isNotEmpty 0com.quickspender.android.ui.screens.transactions  launch 0com.quickspender.android.ui.screens.transactions  let 0com.quickspender.android.ui.screens.transactions  mutableStateOf 0com.quickspender.android.ui.screens.transactions  	onFailure 0com.quickspender.android.ui.screens.transactions  	onSuccess 0com.quickspender.android.ui.screens.transactions  padding 0com.quickspender.android.ui.screens.transactions  provideDelegate 0com.quickspender.android.ui.screens.transactions  remember 0com.quickspender.android.ui.screens.transactions  
selectable 0com.quickspender.android.ui.screens.transactions  setValue 0com.quickspender.android.ui.screens.transactions  size 0com.quickspender.android.ui.screens.transactions  spacedBy 0com.quickspender.android.ui.screens.transactions  toDoubleOrNull 0com.quickspender.android.ui.screens.transactions  transactionRepository 0com.quickspender.android.ui.screens.transactions  
categories Fcom.quickspender.android.ui.screens.transactions.AddTransactionUiState  copy Fcom.quickspender.android.ui.screens.transactions.AddTransactionUiState  error Fcom.quickspender.android.ui.screens.transactions.AddTransactionUiState  	isLoading Fcom.quickspender.android.ui.screens.transactions.AddTransactionUiState  transactionAdded Fcom.quickspender.android.ui.screens.transactions.AddTransactionUiState  AddTransactionUiState Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  DateTimeFormatter Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  
LocalDateTime Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  MutableStateFlow Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  _uiState Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  addTransaction Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  asStateFlow Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  categoryRepository Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  launch Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  loadCategories Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  	onFailure Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  	onSuccess Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  transactionRepository Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  uiState Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  viewModelScope Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  copy Gcom.quickspender.android.ui.screens.transactions.TransactionListUiState  error Gcom.quickspender.android.ui.screens.transactions.TransactionListUiState  	isLoading Gcom.quickspender.android.ui.screens.transactions.TransactionListUiState  transactions Gcom.quickspender.android.ui.screens.transactions.TransactionListUiState  MutableStateFlow Icom.quickspender.android.ui.screens.transactions.TransactionListViewModel  TransactionListUiState Icom.quickspender.android.ui.screens.transactions.TransactionListViewModel  _uiState Icom.quickspender.android.ui.screens.transactions.TransactionListViewModel  asStateFlow Icom.quickspender.android.ui.screens.transactions.TransactionListViewModel  launch Icom.quickspender.android.ui.screens.transactions.TransactionListViewModel  loadTransactions Icom.quickspender.android.ui.screens.transactions.TransactionListViewModel  transactionRepository Icom.quickspender.android.ui.screens.transactions.TransactionListViewModel  uiState Icom.quickspender.android.ui.screens.transactions.TransactionListViewModel  viewModelScope Icom.quickspender.android.ui.screens.transactions.TransactionListViewModel  Activity !com.quickspender.android.ui.theme  BackgroundGray !com.quickspender.android.ui.theme  Boolean !com.quickspender.android.ui.theme  Build !com.quickspender.android.ui.theme  CategoryBlue !com.quickspender.android.ui.theme  
Composable !com.quickspender.android.ui.theme  DarkColorScheme !com.quickspender.android.ui.theme  
ExpenseRed !com.quickspender.android.ui.theme  
FontFamily !com.quickspender.android.ui.theme  
FontWeight !com.quickspender.android.ui.theme  IncomeGreen !com.quickspender.android.ui.theme  LightColorScheme !com.quickspender.android.ui.theme  Pink40 !com.quickspender.android.ui.theme  Pink80 !com.quickspender.android.ui.theme  Purple40 !com.quickspender.android.ui.theme  Purple80 !com.quickspender.android.ui.theme  PurpleGrey40 !com.quickspender.android.ui.theme  PurpleGrey80 !com.quickspender.android.ui.theme  QuickSpenderTheme !com.quickspender.android.ui.theme  
Typography !com.quickspender.android.ui.theme  Unit !com.quickspender.android.ui.theme  WindowCompat !com.quickspender.android.ui.theme  Boolean com.quickspender.android.utils  List com.quickspender.android.utils  PasswordStrength com.quickspender.android.utils  PasswordValidationResult com.quickspender.android.utils  PasswordValidator com.quickspender.android.utils  String com.quickspender.android.utils  any com.quickspender.android.utils  isDigit com.quickspender.android.utils  isLowerCase com.quickspender.android.utils  isUpperCase com.quickspender.android.utils  
mutableListOf com.quickspender.android.utils  MEDIUM /com.quickspender.android.utils.PasswordStrength  STRONG /com.quickspender.android.utils.PasswordStrength  WEAK /com.quickspender.android.utils.PasswordStrength  errors 7com.quickspender.android.utils.PasswordValidationResult  isValid 7com.quickspender.android.utils.PasswordValidationResult  PasswordStrength 0com.quickspender.android.utils.PasswordValidator  PasswordValidationResult 0com.quickspender.android.utils.PasswordValidator  any 0com.quickspender.android.utils.PasswordValidator  getPasswordStrength 0com.quickspender.android.utils.PasswordValidator  isDigit 0com.quickspender.android.utils.PasswordValidator  isLowerCase 0com.quickspender.android.utils.PasswordValidator  isUpperCase 0com.quickspender.android.utils.PasswordValidator  
mutableListOf 0com.quickspender.android.utils.PasswordValidator  validatePassword 0com.quickspender.android.utils.PasswordValidator  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  Class 	java.lang  	Exception 	java.lang  message java.lang.Exception  
BigDecimal 	java.math  
BigInteger 	java.math  SecureRandom 
java.security  X509Certificate java.security.cert  	LocalDate 	java.time  
LocalDateTime 	java.time  format java.time.LocalDate  now java.time.LocalDate  withDayOfMonth java.time.LocalDate  format java.time.LocalDateTime  now java.time.LocalDateTime  parse java.time.LocalDateTime  DateTimeFormatter java.time.format  ISO_LOCAL_DATE "java.time.format.DateTimeFormatter  ISO_LOCAL_DATE_TIME "java.time.format.DateTimeFormatter  	ofPattern "java.time.format.DateTimeFormatter  Inject javax.inject  	Singleton javax.inject  HostnameVerifier 
javax.net.ssl  
SSLContext 
javax.net.ssl  TrustManager 
javax.net.ssl  X509TrustManager 
javax.net.ssl  <SAM-CONSTRUCTOR> javax.net.ssl.HostnameVerifier  getInstance javax.net.ssl.SSLContext  init javax.net.ssl.SSLContext  
socketFactory javax.net.ssl.SSLContext  Array kotlin  CharSequence kotlin  Enum kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Nothing kotlin  OptIn kotlin  Result kotlin  String kotlin  	Throwable kotlin  Triple kotlin  apply kotlin  arrayOf kotlin  let kotlin  map kotlin  	onFailure kotlin  	onSuccess kotlin  synchronized kotlin  get kotlin.Array  not kotlin.Boolean  isDigit kotlin.Char  isLowerCase kotlin.Char  isUpperCase kotlin.Char  isEmpty kotlin.CharSequence  	compareTo 
kotlin.Double  minus 
kotlin.Double  sp 
kotlin.Double  CategoryType kotlin.Enum  	Companion kotlin.Enum  EXPENSE kotlin.Enum  Int kotlin.Enum  TransactionType kotlin.Enum  entries kotlin.Enum  find kotlin.Enum  EXPENSE kotlin.Enum.Companion  entries kotlin.Enum.Companion  find kotlin.Enum.Companion  invoke kotlin.Function0  	compareTo 
kotlin.Int  	Companion 
kotlin.Result  failure 
kotlin.Result  	onFailure 
kotlin.Result  	onSuccess 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  	Companion 
kotlin.String  any 
kotlin.String  format 
kotlin.String  ifBlank 
kotlin.String  isEmpty 
kotlin.String  
isNotBlank 
kotlin.String  
isNotEmpty 
kotlin.String  length 
kotlin.String  let 
kotlin.String  toDoubleOrNull 
kotlin.String  format kotlin.String.Companion  message kotlin.Throwable  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  List kotlin.collections  Map kotlin.collections  MutableList kotlin.collections  any kotlin.collections  	emptyList kotlin.collections  filter kotlin.collections  find kotlin.collections  forEach kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  map kotlin.collections  
mutableListOf kotlin.collections  sumOf kotlin.collections  sumOfDouble kotlin.collections  take kotlin.collections  filter kotlin.collections.List  find kotlin.collections.List  
isNotEmpty kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  sumOf kotlin.collections.List  take kotlin.collections.List  add kotlin.collections.MutableList  isEmpty kotlin.collections.MutableList  SuspendFunction1 kotlin.coroutines  EnumEntries kotlin.enums  find kotlin.enums.EnumEntries  Volatile 
kotlin.jvm  java 
kotlin.jvm  ReadOnlyProperty kotlin.properties  getValue "kotlin.properties.ReadOnlyProperty  provideDelegate "kotlin.properties.ReadOnlyProperty  KClass kotlin.reflect  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  
KProperty1 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  any kotlin.sequences  filter kotlin.sequences  find kotlin.sequences  forEach kotlin.sequences  map kotlin.sequences  sumOf kotlin.sequences  take kotlin.sequences  any kotlin.sequences.Sequence  any kotlin.text  filter kotlin.text  find kotlin.text  forEach kotlin.text  format kotlin.text  ifBlank kotlin.text  isDigit kotlin.text  isEmpty kotlin.text  isLowerCase kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  isUpperCase kotlin.text  map kotlin.text  sumOf kotlin.text  take kotlin.text  toDoubleOrNull kotlin.text  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  runBlocking kotlinx.coroutines  DateTimeFormatter !kotlinx.coroutines.CoroutineScope  	LocalDate !kotlinx.coroutines.CoroutineScope  
LocalDateTime !kotlinx.coroutines.CoroutineScope  _uiState !kotlinx.coroutines.CoroutineScope  authRepository !kotlinx.coroutines.CoroutineScope  categoryRepository !kotlinx.coroutines.CoroutineScope  filter !kotlinx.coroutines.CoroutineScope  first !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  	onFailure !kotlinx.coroutines.CoroutineScope  	onSuccess !kotlinx.coroutines.CoroutineScope  sumOf !kotlinx.coroutines.CoroutineScope  take !kotlinx.coroutines.CoroutineScope  transactionRepository !kotlinx.coroutines.CoroutineScope  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  first kotlinx.coroutines.flow  map kotlinx.coroutines.flow  collectAsState kotlinx.coroutines.flow.Flow  first kotlinx.coroutines.flow.Flow  map kotlinx.coroutines.flow.Flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  value (kotlinx.coroutines.flow.MutableStateFlow  collectAsState !kotlinx.coroutines.flow.StateFlow  Serializable kotlinx.serialization  Interceptor okhttp3  OkHttpClient okhttp3  Request okhttp3  Response okhttp3  Chain okhttp3.Interceptor  proceed okhttp3.Interceptor.Chain  request okhttp3.Interceptor.Chain  Builder okhttp3.OkHttpClient  	Companion okhttp3.OkHttpClient  addInterceptor okhttp3.OkHttpClient.Builder  build okhttp3.OkHttpClient.Builder  hostnameVerifier okhttp3.OkHttpClient.Builder  sslSocketFactory okhttp3.OkHttpClient.Builder  Builder okhttp3.Request  
newBuilder okhttp3.Request  	addHeader okhttp3.Request.Builder  build okhttp3.Request.Builder  HttpLoggingInterceptor okhttp3.logging  HttpLoggingInterceptor &okhttp3.logging.HttpLoggingInterceptor  Level &okhttp3.logging.HttpLoggingInterceptor  apply &okhttp3.logging.HttpLoggingInterceptor  level &okhttp3.logging.HttpLoggingInterceptor  BODY ,okhttp3.logging.HttpLoggingInterceptor.Level  Response 	retrofit2  Retrofit 	retrofit2  body retrofit2.Response  isSuccessful retrofit2.Response  message retrofit2.Response  Builder retrofit2.Retrofit  create retrofit2.Retrofit  addConverterFactory retrofit2.Retrofit.Builder  baseUrl retrofit2.Retrofit.Builder  build retrofit2.Retrofit.Builder  client retrofit2.Retrofit.Builder  GsonConverterFactory retrofit2.converter.gson  create -retrofit2.converter.gson.GsonConverterFactory  Body retrofit2.http  Category retrofit2.http  CreateCategoryRequest retrofit2.http  CreateTransactionRequest retrofit2.http  DELETE retrofit2.http  GET retrofit2.http  Int retrofit2.http  List retrofit2.http  POST retrofit2.http  PUT retrofit2.http  Path retrofit2.http  Query retrofit2.http  Response retrofit2.http  String retrofit2.http  TransactionResponse retrofit2.http  Unit retrofit2.http  UpdateCategoryRequest retrofit2.http  UpdateTransactionRequest retrofit2.http  first "androidx.compose.foundation.layout  first androidx.compose.material3  first androidx.compose.runtime  first 0com.quickspender.android.ui.screens.transactions  first kotlin.collections  first kotlin.collections.List  first 
kotlin.ranges  first kotlin.sequences  first kotlin.text  
isNotEmpty !kotlinx.coroutines.CoroutineScope  println (com.quickspender.android.data.repository  println ;com.quickspender.android.data.repository.CategoryRepository  printStackTrace java.lang.Exception  plus 
kotlin.String  printStackTrace kotlin.Throwable  println 	kotlin.io  ResponseBody okhttp3  string okhttp3.ResponseBody  code retrofit2.Response  	errorBody retrofit2.Response  println com.quickspender.android.di  take com.quickspender.android.di  println )com.quickspender.android.di.NetworkModule  take )com.quickspender.android.di.NetworkModule  take 
kotlin.String  HttpUrl okhttp3  url okhttp3.Request  code okhttp3.Response  isEmpty kotlin.collections.List  Button +androidx.compose.foundation.layout.RowScope  println 0com.quickspender.android.ui.screens.transactions  weight 0com.quickspender.android.ui.screens.transactions  println Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  println !kotlinx.coroutines.CoroutineScope  isBlank "androidx.compose.foundation.layout  isBlank .androidx.compose.foundation.layout.ColumnScope  isBlank androidx.compose.material3  onError &androidx.compose.material3.ColorScheme  isBlank androidx.compose.runtime  ButtonDefaults 0com.quickspender.android.ui.screens.transactions  buttonColors 0com.quickspender.android.ui.screens.transactions  isBlank 0com.quickspender.android.ui.screens.transactions  
clearError Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  
resetState Hcom.quickspender.android.ui.screens.transactions.AddTransactionViewModel  isBlank 
kotlin.String  isBlank kotlin.text  println "androidx.compose.foundation.layout  println .androidx.compose.foundation.layout.ColumnScope  println androidx.compose.material3  	onPrimary &androidx.compose.material3.ColorScheme  onSecondary &androidx.compose.material3.ColorScheme  	secondary &androidx.compose.material3.ColorScheme  println androidx.compose.runtime  println +androidx.compose.foundation.layout.RowScope  d android.util.Log  e android.util.Log  android "androidx.compose.foundation.layout  android .androidx.compose.foundation.layout.ColumnScope  android androidx.compose.material3  android androidx.compose.runtime  android 0com.quickspender.android.ui.screens.transactions  inc 
kotlin.Int  println >com.quickspender.android.data.repository.TransactionRepository  println -com.quickspender.android.ui.screens.dashboard  println @com.quickspender.android.ui.screens.dashboard.DashboardViewModel  AlertDialog "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  AlertDialog .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  AlertDialog +androidx.compose.foundation.layout.RowScope  Arrangement +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  let +androidx.compose.foundation.layout.RowScope  mutableStateOf +androidx.compose.foundation.layout.RowScope  remember +androidx.compose.foundation.layout.RowScope  setValue +androidx.compose.foundation.layout.RowScope  spacedBy +androidx.compose.foundation.layout.RowScope  Delete ,androidx.compose.material.icons.Icons.Filled  Delete &androidx.compose.material.icons.filled  AlertDialog androidx.compose.material3  
IconButton androidx.compose.material3  AlertDialog androidx.compose.runtime  DateTimeFormatter androidx.compose.runtime  	Exception androidx.compose.runtime  
IconButton androidx.compose.runtime  
LocalDateTime androidx.compose.runtime  TransactionResponse androidx.compose.runtime  
formatDate androidx.compose.runtime  deleteTransaction 0com.quickspender.android.data.api.TransactionApi  deleteTransactionById 6com.quickspender.android.data.local.dao.TransactionDao  Unit (com.quickspender.android.data.repository  Unit >com.quickspender.android.data.repository.TransactionRepository  deleteTransaction >com.quickspender.android.data.repository.TransactionRepository  AlertDialog -com.quickspender.android.ui.screens.dashboard  Icon -com.quickspender.android.ui.screens.dashboard  
IconButton -com.quickspender.android.ui.screens.dashboard  Icons -com.quickspender.android.ui.screens.dashboard  
TextButton -com.quickspender.android.ui.screens.dashboard  mutableStateOf -com.quickspender.android.ui.screens.dashboard  remember -com.quickspender.android.ui.screens.dashboard  setValue -com.quickspender.android.ui.screens.dashboard  size -com.quickspender.android.ui.screens.dashboard  loadTransactions 0com.quickspender.android.ui.screens.transactions  
clearError Icom.quickspender.android.ui.screens.transactions.TransactionListViewModel  deleteTransaction Icom.quickspender.android.ui.screens.transactions.TransactionListViewModel  	onFailure Icom.quickspender.android.ui.screens.transactions.TransactionListViewModel  	onSuccess Icom.quickspender.android.ui.screens.transactions.TransactionListViewModel  let kotlin.Function0  loadTransactions !kotlinx.coroutines.CoroutineScope  	getString android.content.Context  R com.quickspender.android  debug_api_base_url !com.quickspender.android.R.string  R com.quickspender.android.di  TimeUnit com.quickspender.android.di  contains com.quickspender.android.di  R )com.quickspender.android.di.NetworkModule  TimeUnit )com.quickspender.android.di.NetworkModule  contains )com.quickspender.android.di.NetworkModule  TimeUnit java.util.concurrent  SECONDS java.util.concurrent.TimeUnit  contains 
kotlin.String  contains kotlin.collections  contains 
kotlin.ranges  contains kotlin.sequences  contains kotlin.text  connectTimeout okhttp3.OkHttpClient.Builder  readTimeout okhttp3.OkHttpClient.Builder  writeTimeout okhttp3.OkHttpClient.Builder  println )com.quickspender.android.data.preferences  println 9com.quickspender.android.data.preferences.UserPreferences  println &com.quickspender.android.ui.navigation  println (com.quickspender.android.ui.screens.auth  take (com.quickspender.android.ui.screens.auth  println 6com.quickspender.android.ui.screens.auth.AuthViewModel  take 6com.quickspender.android.ui.screens.auth.AuthViewModel  contains "androidx.compose.foundation.layout  End .androidx.compose.foundation.layout.Arrangement  contains .androidx.compose.foundation.layout.ColumnScope  contains androidx.compose.material3  
titleSmall %androidx.compose.material3.Typography  contains androidx.compose.runtime  VisualTransformation androidx.compose.ui.text.input  
FontWeight (com.quickspender.android.ui.screens.auth  contains (com.quickspender.android.ui.screens.auth  isBlank (com.quickspender.android.ui.screens.auth  
clearError 6com.quickspender.android.ui.screens.auth.AuthViewModel  to "androidx.compose.foundation.layout  to .androidx.compose.foundation.layout.ColumnScope  to androidx.compose.material3  to androidx.compose.runtime  to (com.quickspender.android.ui.screens.auth  Pair kotlin  to kotlin  
component1 kotlin.Pair  
component2 kotlin.Pair  to 
kotlin.String  replace "androidx.compose.foundation.layout  trim "androidx.compose.foundation.layout  replace .androidx.compose.foundation.layout.ColumnScope  trim .androidx.compose.foundation.layout.ColumnScope  replace androidx.compose.material3  trim androidx.compose.material3  replace androidx.compose.runtime  trim androidx.compose.runtime  replace (com.quickspender.android.ui.screens.auth  trim (com.quickspender.android.ui.screens.auth  replace 
kotlin.String  trim 
kotlin.String  replace kotlin.text  trim kotlin.text  println 7com.quickspender.android.data.repository.AuthRepository                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             