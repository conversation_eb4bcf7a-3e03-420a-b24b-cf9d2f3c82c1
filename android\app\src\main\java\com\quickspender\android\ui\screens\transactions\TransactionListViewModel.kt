package com.quickspender.android.ui.screens.transactions

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.quickspender.android.data.model.Currency
import com.quickspender.android.data.model.TransactionResponse
import com.quickspender.android.data.repository.CurrencyRepository
import com.quickspender.android.data.repository.TransactionRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class TransactionListViewModel @Inject constructor(
    private val transactionRepository: TransactionRepository,
    private val currencyRepository: CurrencyRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(TransactionListUiState())
    val uiState: StateFlow<TransactionListUiState> = _uiState.asStateFlow()
    
    fun loadTransactions() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            try {
                val currentCurrency = currencyRepository.getCurrentCurrencySync()
                val transactions = transactionRepository.getTransactions()
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    transactions = transactions,
                    currentCurrency = currentCurrency
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "Failed to load transactions"
                )
            }
        }
    }

    fun deleteTransaction(transactionId: String) {
        viewModelScope.launch {
            try {
                val result = transactionRepository.deleteTransaction(transactionId)
                result.onSuccess {
                    // Reload transactions after successful deletion
                    loadTransactions()
                }.onFailure { error ->
                    _uiState.value = _uiState.value.copy(
                        error = error.message ?: "Failed to delete transaction"
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    error = e.message ?: "Failed to delete transaction"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

data class TransactionListUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val transactions: List<TransactionResponse> = emptyList(),
    val currentCurrency: Currency = Currency.USD
)
