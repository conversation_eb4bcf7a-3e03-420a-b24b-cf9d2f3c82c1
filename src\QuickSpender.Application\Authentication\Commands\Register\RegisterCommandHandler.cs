using AutoMapper;
using MediatR;
using Microsoft.AspNetCore.Identity;
using QuickSpender.Application.Common.Interfaces;
using QuickSpender.Contracts.Authentication;
using QuickSpender.Domain.Entities;
using QuickSpender.Domain.Enums;

namespace QuickSpender.Application.Authentication.Commands.Register;

public class RegisterCommandHandler : IRequestHandler<RegisterCommand, AuthenticationResponse>
{
    private readonly UserManager<User> _userManager;
    private readonly IJwtTokenGenerator _jwtTokenGenerator;
    private readonly IMapper _mapper;
    private readonly IApplicationDbContext _context;

    public RegisterCommandHandler(
        UserManager<User> userManager,
        IJwtTokenGenerator jwtTokenGenerator,
        IMapper mapper,
        IApplicationDbContext context)
    {
        _userManager = userManager;
        _jwtTokenGenerator = jwtTokenGenerator;
        _mapper = mapper;
        _context = context;
    }

    public async Task<AuthenticationResponse> Handle(RegisterCommand request, CancellationToken cancellationToken)
    {
        // Check if user already exists
        var existingUser = await _userManager.FindByEmailAsync(request.Email);
        if (existingUser is not null)
        {
            throw new InvalidOperationException("User with this email already exists.");
        }

        // Create user
        var user = new User
        {
            FirstName = request.FirstName,
            LastName = request.LastName,
            Email = request.Email,
            UserName = request.Email
        };

        var result = await _userManager.CreateAsync(user, request.Password);
        if (!result.Succeeded)
        {
            throw new InvalidOperationException($"Failed to create user: {string.Join(", ", result.Errors.Select(e => e.Description))}");
        }

        // Create default categories for the new user
        await CreateDefaultCategoriesForUser(user.Id);

        // Generate JWT token
        var token = _jwtTokenGenerator.GenerateToken(user);

        return new AuthenticationResponse(
            user.Id,
            user.FirstName,
            user.LastName,
            user.Email!,
            token
        );
    }

    private async Task CreateDefaultCategoriesForUser(string userId)
    {
        var defaultCategories = new List<Category>
        {
            // Expense Categories
            new Category
            {
                Id = Guid.NewGuid(),
                Name = "Food & Dining",
                Description = "Restaurants, groceries, and food delivery",
                Color = "#FF6B6B",
                Icon = "restaurant",
                Type = CategoryType.Expense,
                IsDefault = false,
                UserId = userId,
                CreatedAt = DateTime.UtcNow
            },
            new Category
            {
                Id = Guid.NewGuid(),
                Name = "Transportation",
                Description = "Gas, public transport, parking, car maintenance",
                Color = "#4ECDC4",
                Icon = "directions_car",
                Type = CategoryType.Expense,
                IsDefault = false,
                UserId = userId,
                CreatedAt = DateTime.UtcNow
            },
            new Category
            {
                Id = Guid.NewGuid(),
                Name = "Shopping",
                Description = "Clothing, electronics, household items",
                Color = "#45B7D1",
                Icon = "shopping_bag",
                Type = CategoryType.Expense,
                IsDefault = false,
                UserId = userId,
                CreatedAt = DateTime.UtcNow
            },
            new Category
            {
                Id = Guid.NewGuid(),
                Name = "Entertainment",
                Description = "Movies, games, hobbies, subscriptions",
                Color = "#96CEB4",
                Icon = "movie",
                Type = CategoryType.Expense,
                IsDefault = false,
                UserId = userId,
                CreatedAt = DateTime.UtcNow
            },
            new Category
            {
                Id = Guid.NewGuid(),
                Name = "Bills & Utilities",
                Description = "Rent, electricity, water, internet, phone",
                Color = "#FFEAA7",
                Icon = "receipt",
                Type = CategoryType.Expense,
                IsDefault = false,
                UserId = userId,
                CreatedAt = DateTime.UtcNow
            },
            new Category
            {
                Id = Guid.NewGuid(),
                Name = "Healthcare",
                Description = "Medical expenses, pharmacy, insurance",
                Color = "#DDA0DD",
                Icon = "local_hospital",
                Type = CategoryType.Expense,
                IsDefault = false,
                UserId = userId,
                CreatedAt = DateTime.UtcNow
            },
            // Income Categories
            new Category
            {
                Id = Guid.NewGuid(),
                Name = "Salary",
                Description = "Monthly salary and wages",
                Color = "#74B9FF",
                Icon = "work",
                Type = CategoryType.Income,
                IsDefault = false,
                UserId = userId,
                CreatedAt = DateTime.UtcNow
            },
            new Category
            {
                Id = Guid.NewGuid(),
                Name = "Freelance",
                Description = "Freelance work and side projects",
                Color = "#A29BFE",
                Icon = "laptop",
                Type = CategoryType.Income,
                IsDefault = false,
                UserId = userId,
                CreatedAt = DateTime.UtcNow
            },
            new Category
            {
                Id = Guid.NewGuid(),
                Name = "Investment",
                Description = "Dividends, interest, capital gains",
                Color = "#6C5CE7",
                Icon = "trending_up",
                Type = CategoryType.Income,
                IsDefault = false,
                UserId = userId,
                CreatedAt = DateTime.UtcNow
            },
            new Category
            {
                Id = Guid.NewGuid(),
                Name = "Other Income",
                Description = "Gifts, bonuses, other sources",
                Color = "#FD79A8",
                Icon = "card_giftcard",
                Type = CategoryType.Income,
                IsDefault = false,
                UserId = userId,
                CreatedAt = DateTime.UtcNow
            }
        };

        _context.Categories.AddRange(defaultCategories);
        await _context.SaveChangesAsync();
    }
}
