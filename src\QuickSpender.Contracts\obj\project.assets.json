{"version": 3, "targets": {"net8.0": {}, "net8.0/linux-x64": {}}, "libraries": {}, "projectFileDependencyGroups": {"net8.0": []}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Contracts\\QuickSpender.Contracts.csproj", "projectName": "QuickSpender.Contracts", "projectPath": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Contracts\\QuickSpender.Contracts.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Contracts\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-x64", "version": "[8.0.8, 8.0.8]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-x64", "version": "[8.0.8, 8.0.8]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-x64": {"#import": []}}}}