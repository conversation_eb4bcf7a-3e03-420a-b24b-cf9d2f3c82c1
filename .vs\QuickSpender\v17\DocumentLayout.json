{"Version": 1, "WorkspaceRootPath": "D:\\Startups\\QuickSpender\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\QuickSpender.API\\QuickSpender.API.csproj|d:\\startups\\quickspender\\src\\quickspender.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\QuickSpender.API\\QuickSpender.API.csproj|solutionrelative:src\\quickspender.api\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{0D0FB8B4-F455-4632-81A4-D03C1323B3FA}|src\\QuickSpender.Infrastructure\\QuickSpender.Infrastructure.csproj|d:\\startups\\quickspender\\src\\quickspender.infrastructure\\persistence\\applicationdbcontextseed.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D0FB8B4-F455-4632-81A4-D03C1323B3FA}|src\\QuickSpender.Infrastructure\\QuickSpender.Infrastructure.csproj|solutionrelative:src\\quickspender.infrastructure\\persistence\\applicationdbcontextseed.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\QuickSpender.API\\QuickSpender.API.csproj|d:\\startups\\quickspender\\src\\quickspender.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\QuickSpender.API\\QuickSpender.API.csproj|solutionrelative:src\\quickspender.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\QuickSpender.API\\QuickSpender.API.csproj|d:\\startups\\quickspender\\src\\quickspender.api\\controllers\\transactionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\QuickSpender.API\\QuickSpender.API.csproj|solutionrelative:src\\quickspender.api\\controllers\\transactionscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D0FB8B4-F455-4632-81A4-D03C1323B3FA}|src\\QuickSpender.Infrastructure\\QuickSpender.Infrastructure.csproj|d:\\startups\\quickspender\\src\\quickspender.infrastructure\\authentication\\jwttokengenerator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D0FB8B4-F455-4632-81A4-D03C1323B3FA}|src\\QuickSpender.Infrastructure\\QuickSpender.Infrastructure.csproj|solutionrelative:src\\quickspender.infrastructure\\authentication\\jwttokengenerator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{0D0FB8B4-F455-4632-81A4-D03C1323B3FA}|src\\QuickSpender.Infrastructure\\QuickSpender.Infrastructure.csproj|d:\\startups\\quickspender\\src\\quickspender.infrastructure\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{0D0FB8B4-F455-4632-81A4-D03C1323B3FA}|src\\QuickSpender.Infrastructure\\QuickSpender.Infrastructure.csproj|solutionrelative:src\\quickspender.infrastructure\\dependencyinjection.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{096E9BA6-A439-42A3-9977-68B64EA9A723}|src\\QuickSpender.Application\\QuickSpender.Application.csproj|d:\\startups\\quickspender\\src\\quickspender.application\\authentication\\queries\\login\\loginqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{096E9BA6-A439-42A3-9977-68B64EA9A723}|src\\QuickSpender.Application\\QuickSpender.Application.csproj|solutionrelative:src\\quickspender.application\\authentication\\queries\\login\\loginqueryhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\QuickSpender.API\\QuickSpender.API.csproj|d:\\startups\\quickspender\\src\\quickspender.api\\quickspender.api.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\QuickSpender.API\\QuickSpender.API.csproj|solutionrelative:src\\quickspender.api\\quickspender.api.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\QuickSpender.API\\QuickSpender.API.csproj|d:\\startups\\quickspender\\src\\quickspender.api\\quickspender.api.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\QuickSpender.API\\QuickSpender.API.csproj|solutionrelative:src\\quickspender.api\\quickspender.api.csproj||{04B8AB82-A572-4FEF-95CE-5222444B6B64}|"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Document", "DocumentIndex": 3, "Title": "TransactionsController.cs", "DocumentMoniker": "D:\\Startups\\QuickSpender\\src\\QuickSpender.API\\Controllers\\TransactionsController.cs", "RelativeDocumentMoniker": "src\\QuickSpender.API\\Controllers\\TransactionsController.cs", "ToolTip": "D:\\Startups\\QuickSpender\\src\\QuickSpender.API\\Controllers\\TransactionsController.cs", "RelativeToolTip": "src\\QuickSpender.API\\Controllers\\TransactionsController.cs", "ViewState": "AgIAACMAAAAAAAAAAAAQwCYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T05:14:39.224Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "Program.cs", "DocumentMoniker": "D:\\Startups\\QuickSpender\\src\\QuickSpender.API\\Program.cs", "RelativeDocumentMoniker": "src\\QuickSpender.API\\Program.cs", "ToolTip": "D:\\Startups\\QuickSpender\\src\\QuickSpender.API\\Program.cs", "RelativeToolTip": "src\\QuickSpender.API\\Program.cs", "ViewState": "AgIAAFEAAAAAAAAAAAAAAGYAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-24T05:10:26.72Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "appsettings.json", "DocumentMoniker": "D:\\Startups\\QuickSpender\\src\\QuickSpender.API\\appsettings.json", "RelativeDocumentMoniker": "src\\QuickSpender.API\\appsettings.json", "ToolTip": "D:\\Startups\\QuickSpender\\src\\QuickSpender.API\\appsettings.json", "RelativeToolTip": "src\\QuickSpender.API\\appsettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-20T03:59:34.603Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "ApplicationDbContextSeed.cs", "DocumentMoniker": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Infrastructure\\Persistence\\ApplicationDbContextSeed.cs", "RelativeDocumentMoniker": "src\\QuickSpender.Infrastructure\\Persistence\\ApplicationDbContextSeed.cs", "ToolTip": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Infrastructure\\Persistence\\ApplicationDbContextSeed.cs", "RelativeToolTip": "src\\QuickSpender.Infrastructure\\Persistence\\ApplicationDbContextSeed.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAAA8AAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T03:59:22.239Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "JwtTokenGenerator.cs", "DocumentMoniker": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Infrastructure\\Authentication\\JwtTokenGenerator.cs", "RelativeDocumentMoniker": "src\\QuickSpender.Infrastructure\\Authentication\\JwtTokenGenerator.cs", "ToolTip": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Infrastructure\\Authentication\\JwtTokenGenerator.cs", "RelativeToolTip": "src\\QuickSpender.Infrastructure\\Authentication\\JwtTokenGenerator.cs", "ViewState": "AgIAABgAAAAAAAAAAAAowAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T03:59:12.293Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "DependencyInjection.cs", "DocumentMoniker": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Infrastructure\\DependencyInjection.cs", "RelativeDocumentMoniker": "src\\QuickSpender.Infrastructure\\DependencyInjection.cs", "ToolTip": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Infrastructure\\DependencyInjection.cs", "RelativeToolTip": "src\\QuickSpender.Infrastructure\\DependencyInjection.cs", "ViewState": "AgIAAAYAAAAAAAAAAAAAABIAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-20T03:58:40.624Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "QuickSpender.API", "DocumentMoniker": "D:\\Startups\\QuickSpender\\src\\QuickSpender.API\\QuickSpender.API.csproj", "RelativeDocumentMoniker": "src\\QuickSpender.API\\QuickSpender.API.csproj", "ToolTip": "D:\\Startups\\QuickSpender\\src\\QuickSpender.API\\QuickSpender.API.csproj", "RelativeToolTip": "src\\QuickSpender.API\\QuickSpender.API.csproj", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-12T05:09:05.757Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "QuickSpender.API.csproj", "DocumentMoniker": "D:\\Startups\\QuickSpender\\src\\QuickSpender.API\\QuickSpender.API.csproj", "RelativeDocumentMoniker": "src\\QuickSpender.API\\QuickSpender.API.csproj", "ToolTip": "D:\\Startups\\QuickSpender\\src\\QuickSpender.API\\QuickSpender.API.csproj", "RelativeToolTip": "src\\QuickSpender.API\\QuickSpender.API.csproj", "ViewState": "AgIAAAwAAAAAAAAAAAAQwBYAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-06-12T05:08:05.05Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "LoginQueryHandler.cs", "DocumentMoniker": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Application\\Authentication\\Queries\\Login\\LoginQueryHandler.cs", "RelativeDocumentMoniker": "src\\QuickSpender.Application\\Authentication\\Queries\\Login\\LoginQueryHandler.cs", "ToolTip": "D:\\Startups\\QuickSpender\\src\\QuickSpender.Application\\Authentication\\Queries\\Login\\LoginQueryHandler.cs", "RelativeToolTip": "src\\QuickSpender.Application\\Authentication\\Queries\\Login\\LoginQueryHandler.cs", "ViewState": "AgIAAAcAAAAAAAAAAAAYwBAAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-12T05:00:34.363Z"}]}]}]}