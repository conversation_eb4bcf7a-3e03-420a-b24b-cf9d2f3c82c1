  Activity android.app  Context android.content  ContextWrapper android.content  ContextThemeWrapper android.view  ComponentActivity androidx.activity  ComponentActivity androidx.core.app  	ViewModel androidx.lifecycle  OnConflictStrategy 
androidx.room  RoomDatabase 
androidx.room  	Companion  androidx.room.OnConflictStrategy  MainActivity com.quickspender.android  AuthApi !com.quickspender.android.data.api  CategoryApi !com.quickspender.android.data.api  	ReportApi !com.quickspender.android.data.api  TransactionApi !com.quickspender.android.data.api  QuickSpenderDatabase #com.quickspender.android.data.local  	Companion 8com.quickspender.android.data.local.QuickSpenderDatabase  CategoryDao 'com.quickspender.android.data.local.dao  TransactionDao 'com.quickspender.android.data.local.dao  Category #com.quickspender.android.data.model  Transaction #com.quickspender.android.data.model  TransactionWithCategory #com.quickspender.android.data.model  UserPreferences )com.quickspender.android.data.preferences  AuthRepository (com.quickspender.android.data.repository  CategoryRepository (com.quickspender.android.data.repository  ReportRepository (com.quickspender.android.data.repository  TransactionRepository (com.quickspender.android.data.repository  DatabaseModule com.quickspender.android.di  
NetworkModule com.quickspender.android.di  
AuthViewModel (com.quickspender.android.ui.screens.auth  CategoryManagementViewModel .com.quickspender.android.ui.screens.categories  DashboardViewModel -com.quickspender.android.ui.screens.dashboard  AddTransactionViewModel 0com.quickspender.android.ui.screens.transactions  TransactionListViewModel 0com.quickspender.android.ui.screens.transactions                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    