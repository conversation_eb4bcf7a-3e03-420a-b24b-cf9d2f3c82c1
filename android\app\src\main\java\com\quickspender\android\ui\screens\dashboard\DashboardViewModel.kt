package com.quickspender.android.ui.screens.dashboard

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.quickspender.android.data.model.CategorySummaryResponse
import com.quickspender.android.data.model.TransactionResponse
import com.quickspender.android.data.repository.ReportRepository
import com.quickspender.android.data.repository.TransactionRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import javax.inject.Inject

@HiltViewModel
class DashboardViewModel @Inject constructor(
    private val transactionRepository: TransactionRepository,
    private val reportRepository: ReportRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(DashboardUiState())
    val uiState: StateFlow<DashboardUiState> = _uiState.asStateFlow()
    
    fun loadDashboardData() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)

            try {
                // Get last 60 days of data (wider range to catch all transactions)
                val now = LocalDate.now()
                val startDate = now.minusDays(60)
                val endDate = now.plusDays(1) // Include tomorrow to catch today's transactions
                val formatter = DateTimeFormatter.ISO_LOCAL_DATE

                // Load transactions and summary data
                val transactions = transactionRepository.getTransactions(
                    fromDate = startDate.format(formatter),
                    toDate = endDate.format(formatter)
                )

                val summaryResult = reportRepository.getSummary(
                    fromDate = startDate.format(formatter),
                    toDate = endDate.format(formatter)
                )

                if (summaryResult.isSuccess) {
                    val summary = summaryResult.getOrThrow()
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        totalIncome = summary.totalIncome,
                        totalExpenses = summary.totalExpenses,
                        balance = summary.balance,
                        recentTransactions = transactions.take(10),
                        expensesByCategory = summary.expensesByCategory,
                        incomeByCategory = summary.incomeByCategory
                    )
                } else {
                    // Fallback to basic calculation if summary fails
                    val income = transactions.filter { it.type == 1 }.sumOf { it.amount }
                    val expenses = transactions.filter { it.type == 0 }.sumOf { it.amount }
                    val balance = income - expenses

                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        totalIncome = income,
                        totalExpenses = expenses,
                        balance = balance,
                        recentTransactions = transactions.take(10)
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "Failed to load dashboard data"
                )
            }
        }
    }
}

data class DashboardUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val totalIncome: Double = 0.0,
    val totalExpenses: Double = 0.0,
    val balance: Double = 0.0,
    val recentTransactions: List<TransactionResponse> = emptyList(),
    val expensesByCategory: List<CategorySummaryResponse> = emptyList(),
    val incomeByCategory: List<CategorySummaryResponse> = emptyList()
)
