{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0/linux-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {}, ".NETCoreApp,Version=v8.0/linux-x64": {"QuickSpender.API/1.0.0": {"dependencies": {"FluentValidation.AspNetCore": "11.3.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.0", "Microsoft.EntityFrameworkCore.Design": "8.0.0", "QuickSpender.Application": "1.0.0", "QuickSpender.Contracts": "1.0.0", "QuickSpender.Infrastructure": "1.0.0", "Swashbuckle.AspNetCore": "6.5.0", "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64": "8.0.8", "runtimepack.Microsoft.AspNetCore.App.Runtime.linux-x64": "8.0.8"}, "runtime": {"QuickSpender.API.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64/8.0.8": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.824.36612"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "8.0.824.36612"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "netstandard.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "8.0.824.36612"}}, "native": {"createdump": {"fileVersion": "0.0.0.0"}, "libSystem.Globalization.Native.so": {"fileVersion": "0.0.0.0"}, "libSystem.IO.Compression.Native.so": {"fileVersion": "0.0.0.0"}, "libSystem.Native.so": {"fileVersion": "0.0.0.0"}, "libSystem.Net.Security.Native.so": {"fileVersion": "0.0.0.0"}, "libSystem.Security.Cryptography.Native.OpenSsl.so": {"fileVersion": "0.0.0.0"}, "libclrgc.so": {"fileVersion": "0.0.0.0"}, "libclrjit.so": {"fileVersion": "0.0.0.0"}, "libcoreclr.so": {"fileVersion": "0.0.0.0"}, "libcoreclrtraceptprovider.so": {"fileVersion": "0.0.0.0"}, "libhostfxr.so": {"fileVersion": "0.0.0.0"}, "libhostpolicy.so": {"fileVersion": "0.0.0.0"}, "libmscordaccore.so": {"fileVersion": "0.0.0.0"}, "libmscordbi.so": {"fileVersion": "0.0.0.0"}}}, "runtimepack.Microsoft.AspNetCore.App.Runtime.linux-x64/8.0.8": {"runtime": {"Microsoft.AspNetCore.Antiforgery.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Authentication.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Authentication.BearerToken.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Authentication.Cookies.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Authentication.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Authentication.OAuth.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Authentication.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Authorization.Policy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Components.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Components.Endpoints.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Components.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Components.Server.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Components.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Components.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Connections.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.CookiePolicy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Cors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.DataProtection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Diagnostics.HealthChecks.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.HostFiltering.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Html.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Http.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Http.Connections.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Http.Connections.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Http.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Http.Features.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Http.Results.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.HttpLogging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.HttpOverrides.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.HttpsPolicy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Identity.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Localization.Routing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.Cors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.Formatters.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.Razor.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.RazorPages.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.TagHelpers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.OutputCaching.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.RateLimiting.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Razor.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Razor.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.RequestDecompression.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.ResponseCaching.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.ResponseCompression.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Rewrite.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Routing.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Routing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Server.HttpSys.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Server.IIS.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Server.IISIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Server.Kestrel.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.NamedPipes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Server.Kestrel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.Session.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.SignalR.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.SignalR.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.SignalR.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.StaticFiles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.WebUtilities.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.AspNetCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.724.31311"}, "Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.724.31311"}, "Microsoft.Extensions.Configuration.Ini.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Configuration.KeyPerFile.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Configuration.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.Diagnostics.HealthChecks.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Features.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.FileProviders.Composite.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.FileProviders.Embedded.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Identity.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.Identity.Stores.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.324.11423"}, "Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Logging.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Options.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.224.6711"}, "Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "Microsoft.Extensions.WebEncoders.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.JSInterop.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "Microsoft.Net.Http.Headers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36908"}, "System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}, "System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.524.21615"}, "System.Threading.RateLimiting.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "AutoMapper/12.0.1": {"dependencies": {"Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"dependencies": {"AutoMapper": "12.0.1", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "FluentValidation/11.8.1": {"runtime": {"lib/net7.0/FluentValidation.dll": {"assemblyVersion": "1*******", "fileVersion": "********"}}}, "FluentValidation.AspNetCore/11.3.0": {"dependencies": {"FluentValidation": "11.8.1", "FluentValidation.DependencyInjectionExtensions": "11.8.1"}, "runtime": {"lib/net6.0/FluentValidation.AspNetCore.dll": {"assemblyVersion": "1*******", "fileVersion": "11.3.0.0"}}}, "FluentValidation.DependencyInjectionExtensions/11.8.1": {"dependencies": {"FluentValidation": "11.8.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {"assemblyVersion": "1*******", "fileVersion": "********"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "MediatR/12.2.0": {"dependencies": {"MediatR.Contracts": "2.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}, "runtime": {"lib/net6.0/MediatR.dll": {"assemblyVersion": "********", "fileVersion": "12.2.0.0"}}}, "MediatR.Contracts/2.0.1": {"runtime": {"lib/netstandard2.0/MediatR.Contracts.dll": {"assemblyVersion": "2.0.1.0", "fileVersion": "2.0.1.0"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.0": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.0.3"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.AspNetCore.Cryptography.Internal/8.0.0": {}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/8.0.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "8.0.0"}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "8.0.0", "Microsoft.Extensions.Identity.Stores": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {}, "Microsoft.CodeAnalysis.Common/4.5.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.3", "System.Collections.Immutable": "6.0.0", "System.Reflection.Metadata": "6.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.5.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.5.0", "Microsoft.CodeAnalysis.Common": "4.5.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.5.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.CodeAnalysis.Common": "4.5.0", "System.Composition": "6.0.0", "System.IO.Pipelines": "6.0.3", "System.Threading.Channels": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.EntityFrameworkCore/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.0", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.0": {}, "Microsoft.EntityFrameworkCore.Design/8.0.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.5.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.0", "Microsoft.Extensions.DependencyModel": "8.0.0", "Mono.TextTemplating": "2.2.1"}}, "Microsoft.EntityFrameworkCore.Relational/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "Microsoft.Extensions.DependencyModel/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Identity.Core/8.0.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Identity.Stores/8.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Identity.Core": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2"}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.IdentityModel.Abstractions/8.0.2": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.2.50822"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.0.2": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.2.50822"}}}, "Microsoft.IdentityModel.Logging/8.0.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.2.50822"}}}, "Microsoft.IdentityModel.Protocols/7.0.3": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.0.2", "Microsoft.IdentityModel.Tokens": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "7.0.3.0", "fileVersion": "7.0.3.41017"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.0.3": {"dependencies": {"Microsoft.IdentityModel.Protocols": "7.0.3", "System.IdentityModel.Tokens.Jwt": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "7.0.3.0", "fileVersion": "7.0.3.41017"}}}, "Microsoft.IdentityModel.Tokens/8.0.2": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.0.2"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.2.50822"}}}, "Microsoft.OpenApi/1.2.3": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.2.3.0", "fileVersion": "1.2.3.0"}}}, "Mono.TextTemplating/2.2.1": {"dependencies": {"System.CodeDom": "4.4.0"}, "runtime": {"lib/netstandard2.0/Mono.TextTemplating.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.1.1"}}}, "Npgsql/8.0.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Npgsql.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.EntityFrameworkCore.Abstractions": "8.0.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.0", "Npgsql": "8.0.0"}, "runtime": {"lib/net8.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore/6.5.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.5.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.5.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.5.0"}}, "Swashbuckle.AspNetCore.Swagger/6.5.0": {"dependencies": {"Microsoft.OpenApi": "1.2.3"}, "runtime": {"lib/net7.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.5.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.5.0"}, "runtime": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.5.0": {"runtime": {"lib/net7.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.CodeDom/4.4.0": {"runtime": {"lib/netstandard2.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.25519.3"}}}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Composition/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Convention": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0", "System.Composition.TypedParts": "6.0.0"}}, "System.Composition.AttributedModel/6.0.0": {"runtime": {"lib/net6.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Convention/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Hosting/6.0.0": {"dependencies": {"System.Composition.Runtime": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Runtime/6.0.0": {"runtime": {"lib/net6.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.TypedParts/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.IdentityModel.Tokens.Jwt/8.0.2": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.0.2", "Microsoft.IdentityModel.Tokens": "8.0.2"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.2.50822"}}}, "System.IO.Pipelines/6.0.3": {}, "System.Reflection.Metadata/6.0.1": {"dependencies": {"System.Collections.Immutable": "6.0.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Threading.Channels/6.0.0": {}, "QuickSpender.Application/1.0.0": {"dependencies": {"AutoMapper": "12.0.1", "AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "FluentValidation": "11.8.1", "FluentValidation.DependencyInjectionExtensions": "11.8.1", "MediatR": "12.2.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "QuickSpender.Contracts": "1.0.0", "QuickSpender.Domain": "1.0.0"}, "runtime": {"QuickSpender.Application.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "QuickSpender.Contracts/1.0.0": {"runtime": {"QuickSpender.Contracts.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "QuickSpender.Domain/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": "8.0.0"}, "runtime": {"QuickSpender.Domain.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}, "QuickSpender.Infrastructure/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": "8.0.0", "Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.EntityFrameworkCore.Design": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.IdentityModel.Tokens": "8.0.2", "Npgsql.EntityFrameworkCore.PostgreSQL": "8.0.0", "QuickSpender.Application": "1.0.0", "QuickSpender.Domain": "1.0.0", "System.IdentityModel.Tokens.Jwt": "8.0.2"}, "runtime": {"QuickSpender.Infrastructure.dll": {"assemblyVersion": "1.0.0", "fileVersion": "*******"}}}}}, "libraries": {"QuickSpender.API/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64/8.0.8": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.AspNetCore.App.Runtime.linux-x64/8.0.8": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "AutoMapper/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "path": "automapper/12.0.1", "hashPath": "automapper.12.0.1.nupkg.sha512"}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+g/K+Vpe3gGMKGzjslMOdqNlkikScDjWfVvmWTayrDHaG/n2pPmFBMa+jKX1r/h6BDGFdkyRjAuhFE3ykW+r1g==", "path": "automapper.extensions.microsoft.dependencyinjection/12.0.1", "hashPath": "automapper.extensions.microsoft.dependencyinjection.12.0.1.nupkg.sha512"}, "FluentValidation/11.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-N72rnlE99XYB7EGA1u9y7m7kNTTynqOPBhZqDE8zr1Y0aSR4t5si94LRA7UVdAV09GaXWCErW+EiFhfbg3DSbg==", "path": "fluentvalidation/11.8.1", "hashPath": "fluentvalidation.11.8.1.nupkg.sha512"}, "FluentValidation.AspNetCore/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jtFVgKnDFySyBlPS8bZbTKEEwJZnn11rXXJ2SQnjDhZ56rQqybBg9Joq4crRLz3y0QR8WoOq4iE4piV81w/Djg==", "path": "fluentvalidation.aspnetcore/11.3.0", "hashPath": "fluentvalidation.aspnetcore.11.3.0.nupkg.sha512"}, "FluentValidation.DependencyInjectionExtensions/11.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-Yyme2uCHnf+HawEHUEj7PguR54TPkx1HJT93/POvHT4xONvUrNfLg3Zt2NUAmIbhMjplb6TPLN8DD9AA7B/3QA==", "path": "fluentvalidation.dependencyinjectionextensions/11.8.1", "hashPath": "fluentvalidation.dependencyinjectionextensions.11.8.1.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "MediatR/12.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-8TUFrHapKi6D74PhnSNEguRsH91HNGyP3R4ZQdgDorJgl9Wac5Prh0vA33QfrniAaS6L2xNNhc6vxzg+5AIbwA==", "path": "mediatr/12.2.0", "hashPath": "mediatr.12.2.0.nupkg.sha512"}, "MediatR.Contracts/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-FYv95bNT4UwcNA+G/J1oX5OpRiSUxteXaUt2BJbRSdRNiIUNbggJF69wy6mnk2wYToaanpdXZdCwVylt96MpwQ==", "path": "mediatr.contracts/2.0.1", "hashPath": "mediatr.contracts.2.0.1.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rwxaZYHips5M9vqxRkGfJthTx+Ws4O4yCuefn17J371jL3ouC5Ker43h2hXb5yd9BMnImE9rznT75KJHm6bMgg==", "path": "microsoft.aspnetcore.authentication.jwtbearer/8.0.0", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-buuMMCTxFcVkOkEftb2OafYxrveNGre9KJF4Oi1DkR4rxIj6oLam7Wq3g0Fp9hNVpJteKEPiupsxYnPrD/oUGA==", "path": "microsoft.aspnetcore.cryptography.internal/8.0.0", "hashPath": "microsoft.aspnetcore.cryptography.internal.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-65w93R5wqUUs35R9wjHHDf75GqAbxJsNByKZo5TbQOWSXcUbLWrDUWBQHv78iXIT0PL1pXNqKQz7OHiHMvo0/A==", "path": "microsoft.aspnetcore.cryptography.keyderivation/8.0.0", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ua2LSZY/f0BkNUUVPPm83eq4Xnt+FZYutiMimRrzSmv2K2t2Ia/PuP4CfibYNSwnKl6fbZ49Bwn2mQGWnmmvOA==", "path": "microsoft.aspnetcore.identity.entityframeworkcore/8.0.0", "hashPath": "microsoft.aspnetcore.identity.entityframeworkcore.8.0.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-j/rOZtLMVJjrfLRlAMckJLPW/1rze9MT1yfWqSIbUPGRu1m1P0fuo9PmqapwsmePfGB5PJrudQLvmUOAMF0DqQ==", "path": "microsoft.codeanalysis.analyzers/3.3.3", "hashPath": "microsoft.codeanalysis.analyzers.3.3.3.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-lwAbIZNdnY0SUNoDmZHkVUwLO8UyNnyyh1t/4XsbFxi4Ounb3xszIYZaWhyj5ZjyfcwqwmtMbE7fUTVCqQEIdQ==", "path": "microsoft.codeanalysis.common/4.5.0", "hashPath": "microsoft.codeanalysis.common.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-cM59oMKAOxvdv76bdmaKPy5hfj+oR+zxikWoueEB7CwTko7mt9sVKZI8Qxlov0C/LuKEG+WQwifepqL3vuTiBQ==", "path": "microsoft.codeanalysis.csharp/4.5.0", "hashPath": "microsoft.codeanalysis.csharp.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-h74wTpmGOp4yS4hj+EvNzEiPgg/KVs2wmSfTZ81upJZOtPkJsVkgfsgtxxqmAeapjT/vLKfmYV0bS8n5MNVP+g==", "path": "microsoft.codeanalysis.csharp.workspaces/4.5.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4dDRmGELXG72XZaonnOeORyD/T5RpEu5LGHOUIhnv+MmUWDY/m1kWXGwtcgQ5CJ5ynkFiRnIYzTKXYjUs7rbw==", "path": "microsoft.codeanalysis.workspaces.common/4.5.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.5.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SoODat83pGQUpWB9xULdMX6tuKpq/RTXDuJ2WeC1ldUKcKzLkaFJD1n+I0nOLY58odez/e7z8b6zdp235G/kyg==", "path": "microsoft.entityframeworkcore/8.0.0", "hashPath": "microsoft.entityframeworkcore.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR22s3+zoqlVI7xauFKn1znSIFHO8xuILT+noSwS8bZCKcHz0ydkTDQMuaxSa5WBaQrZmwtTz9rmRvJ7X8mSPQ==", "path": "microsoft.entityframeworkcore.abstractions/8.0.0", "hashPath": "microsoft.entityframeworkcore.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZXxEeLs2zoZ1TA+QoMMcw4f3Tirf8PzgdDax8RoWo0dxI2KmqiEGWYjhm2B/XyWfglc6+mNRyB8rZiQSmxCpeg==", "path": "microsoft.entityframeworkcore.analyzers/8.0.0", "hashPath": "microsoft.entityframeworkcore.analyzers.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-94reKYu63jg4O75UI3LMJHwOSi8tQ6IfubiZhdnSsWcgtmAuF8OyLfjK/MIxuvaQRJZAF6E747FIuxjOtb8/og==", "path": "microsoft.entityframeworkcore.design/8.0.0", "hashPath": "microsoft.entityframeworkcore.design.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fFKkr24cYc7Zw5T6DC4tEyOEPgPbq23BBmym1r9kn4ET9F3HKaetpOeQtV2RryYyUxEeNkJuxgfiZHTisqZc+A==", "path": "microsoft.entityframeworkcore.relational/8.0.0", "hashPath": "microsoft.entityframeworkcore.relational.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "path": "microsoft.extensions.caching.memory/8.0.0", "hashPath": "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-3iE7UF7MQkCv1cxzCahz+Y/guQbTqieyxyaWKhrRO91itI9cOKO76OHeQDahqG4MmW5umr3CcCvGmK92lWNlbg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.2", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NSmDw3K0ozNDgShSIpsZcbFIzBX4w28nDag+TfaQujkXGazBm+lid5onlWoCBy4VsLxqnnKjEBbGSJVWJMf43g==", "path": "microsoft.extensions.dependencymodel/8.0.0", "hashPath": "microsoft.extensions.dependencymodel.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hnXHyIQc+uc2uNMcIbr43+oNBAPEhMpW6lE8ux3MOegRz50WBna4AItlZDY7Y+Id1LLBbf73osUqeTw7CQ371w==", "path": "microsoft.extensions.identity.core/8.0.0", "hashPath": "microsoft.extensions.identity.core.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Stores/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DmDCpSpngZDBm44wVmxCeYs4HGJr/m32jMItp6pfb7KKtqWYw2vybHRg880j18k/eSFyM4v9uONsnEPgDdi9lg==", "path": "microsoft.extensions.identity.stores/8.0.0", "hashPath": "microsoft.extensions.identity.stores.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-m73Bun0l0jL8rceWZ9TMD4hwQCjDIaRT1s5RMN7TBDpXu8Ea8KcRndo45btW9gG0i/USmHLCmOBIITvTA4Y6PA==", "path": "microsoft.identitymodel.abstractions/8.0.2", "hashPath": "microsoft.identitymodel.abstractions.8.0.2.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-6CVWMfXrQPMUaqlsMfG8OjtyTIKvtgiQCFOJ2YhSZo1UDaAWweVN7jGSrz59Ez0Y8lh260WE5V2b0Oe9NlVlyw==", "path": "microsoft.identitymodel.jsonwebtokens/8.0.2", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.0.2.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-iKUyFKCQgc8rcEqyIJGLOIqqxemG7bgraqS9n5J6RPoZZH7dwxmJd3aFYmxXuAnfznJuaE1DQX5U46Cqvb+BOg==", "path": "microsoft.identitymodel.logging/8.0.2", "hashPath": "microsoft.identitymodel.logging.8.0.2.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-BtwR+tctBYhPNygyZmt1Rnw74GFrJteW+1zcdIgyvBCjkek6cNwPPqRfdhzCv61i+lwyNomRi8+iI4QKd4YCKA==", "path": "microsoft.identitymodel.protocols/7.0.3", "hashPath": "microsoft.identitymodel.protocols.7.0.3.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-W97TraHApDNArLwpPcXfD+FZH7njJsfEwZE9y9BoofeXMS8H0LBBobz0VOmYmMK4mLdOKxzN7SFT3Ekg0FWI3Q==", "path": "microsoft.identitymodel.protocols.openidconnect/7.0.3", "hashPath": "microsoft.identitymodel.protocols.openidconnect.7.0.3.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-X58KyDBpGJZcCfmSgbkxJLLxd04eMFVaJlMEbRCyWL1X44n6kMxRyK6UTS1zgi5DHikeyiZj8bi7+p0kfPepLg==", "path": "microsoft.identitymodel.tokens/8.0.2", "hashPath": "microsoft.identitymodel.tokens.8.0.2.nupkg.sha512"}, "Microsoft.OpenApi/1.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-Nug3rO+7Kl5/SBAadzSMAVgqDlfGjJZ0GenQrLywJ84XGKO0uRqkunz5Wyl0SDwcR71bAATXvSdbdzPrYRYKGw==", "path": "microsoft.openapi/1.2.3", "hashPath": "microsoft.openapi.1.2.3.nupkg.sha512"}, "Mono.TextTemplating/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-KZYeKBET/2Z0gY1WlTAK7+RHTl7GSbtvTLDXEZZojUdAPqpQNDL6tHv7VUpqfX5VEOh+uRGKaZXkuD253nEOBQ==", "path": "mono.texttemplating/2.2.1", "hashPath": "mono.texttemplating.2.2.1.nupkg.sha512"}, "Npgsql/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qiz74U+O7Mv4knrsXgKVYGJjgwoziK+aMFZqz7PtKR3vyGIhZA0tnW6HoUnL3X+YqtmVuhmoKkN8LDWEHMxPbw==", "path": "npgsql/8.0.0", "hashPath": "npgsql.8.0.0.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GDXiMS9peEdjSCU/rfgyHruio7q6tYuywGaktqEi6UPQ6ILechp3fVVX+dHXkIXt4nklCBzYVWkzFrSL9ubKUA==", "path": "npgsql.entityframeworkcore.postgresql/8.0.0", "hashPath": "npgsql.entityframeworkcore.postgresql.8.0.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-FK05XokgjgwlCI6wCT+D4/abtQkL1X1/B9Oas6uIwHFmYrIO9WUD5aLC9IzMs9GnHfUXOtXZ2S43gN1mhs5+aA==", "path": "swashbuckle.aspnetcore/6.5.0", "hashPath": "swashbuckle.aspnetcore.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-XWmCmqyFmoItXKFsQSwQbEAsjDKcxlNf1l+/Ki42hcb6LjKL8m5Db69OTvz5vLonMSRntYO1XLqz0OP+n3vKnA==", "path": "swashbuckle.aspnetcore.swagger/6.5.0", "hashPath": "swashbuckle.aspnetcore.swagger.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y/qW8Qdg9OEs7V013tt+94OdPxbRdbhcEbw4NiwGvf4YBcfhL/y7qp/Mjv/cENsQ2L3NqJ2AOu94weBy/h4KvA==", "path": "swashbuckle.aspnetcore.swaggergen/6.5.0", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-OvbvxX+wL8skxTBttcBsVxdh73Fag4xwqEU2edh4JMn7Ws/xJHnY/JB1e9RoCb6XpDxUF3hD9A0Z1lEUx40Pfw==", "path": "swashbuckle.aspnetcore.swaggerui/6.5.0", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.5.0.nupkg.sha512"}, "System.CodeDom/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "path": "system.codedom/4.4.0", "hashPath": "system.codedom.4.4.0.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Composition/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-d7wMuKQtfsxUa7S13tITC8n1cQzewuhD5iDjZtK2prwFfKVzdYtgrTHgjaV03Zq7feGQ5gkP85tJJntXwInsJA==", "path": "system.composition/6.0.0", "hashPath": "system.composition.6.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WK1nSDLByK/4VoC7fkNiFuTVEiperuCN/Hyn+VN30R+W2ijO1d0Z2Qm0ScEl9xkSn1G2MyapJi8xpf4R8WRa/w==", "path": "system.composition.attributedmodel/6.0.0", "hashPath": "system.composition.attributedmodel.6.0.0.nupkg.sha512"}, "System.Composition.Convention/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XYi4lPRdu5bM4JVJ3/UIHAiG6V6lWWUlkhB9ab4IOq0FrRsp0F4wTyV4Dj+Ds+efoXJ3qbLqlvaUozDO7OLeXA==", "path": "system.composition.convention/6.0.0", "hashPath": "system.composition.convention.6.0.0.nupkg.sha512"}, "System.Composition.Hosting/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-w/wXjj7kvxuHPLdzZ0PAUt++qJl03t7lENmb2Oev0n3zbxyNULbWBlnd5J5WUMMv15kg5o+/TCZFb6lSwfaUUQ==", "path": "system.composition.hosting/6.0.0", "hashPath": "system.composition.hosting.6.0.0.nupkg.sha512"}, "System.Composition.Runtime/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qkRH/YBaMPTnzxrS5RDk1juvqed4A6HOD/CwRcDGyPpYps1J27waBddiiq1y93jk2ZZ9wuA/kynM+NO0kb3PKg==", "path": "system.composition.runtime/6.0.0", "hashPath": "system.composition.runtime.6.0.0.nupkg.sha512"}, "System.Composition.TypedParts/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iUR1eHrL8Cwd82neQCJ00MpwNIBs4NZgXzrPqx8NJf/k4+mwBO0XCRmHYJT4OLSwDDqh5nBLJWkz5cROnrGhRA==", "path": "system.composition.typedparts/6.0.0", "hashPath": "system.composition.typedparts.6.0.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-jbfANr2qEmrfEtK3L7tOnkCW5/y2YiF6ISSRhRBgIZL+W2ZbEVHFNTNV8QOKeNU6gedQnhpdU2IvB0YB3nNMjw==", "path": "system.identitymodel.tokens.jwt/8.0.2", "hashPath": "system.identitymodel.tokens.jwt.8.0.2.nupkg.sha512"}, "System.IO.Pipelines/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-ryTgF+iFkpGZY1vRQhfCzX0xTdlV3pyaTTqRu2ETbEv+HlV7O6y7hyQURnghNIXvctl5DuZ//Dpks6HdL/Txgw==", "path": "system.io.pipelines/6.0.3", "hashPath": "system.io.pipelines.6.0.3.nupkg.sha512"}, "System.Reflection.Metadata/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-III/lNMSn0ZRBuM9m5Cgbiho5j81u0FAEagFX5ta2DKbljZ3T0IpD8j+BIiHQPeKqJppWS9bGEp6JnKnWKze0g==", "path": "system.reflection.metadata/6.0.1", "hashPath": "system.reflection.metadata.6.0.1.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>drZ<PERSON>2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "path": "system.text.json/8.0.0", "hashPath": "system.text.json.8.0.0.nupkg.sha512"}, "System.Threading.Channels/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TY8/9+tI0mNaUMgntOxxaq2ndTkdXqLSxvPmas7XEqOlv9lQtB7wLjYGd756lOaO7Dvb5r/WXhluM+0Xe87v5Q==", "path": "system.threading.channels/6.0.0", "hashPath": "system.threading.channels.6.0.0.nupkg.sha512"}, "QuickSpender.Application/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "QuickSpender.Contracts/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "QuickSpender.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "QuickSpender.Infrastructure/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}, "runtimes": {"android-x64": ["android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-bionic-x64": ["linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-musl-x64": ["linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-x64": ["linux", "unix-x64", "unix", "any", "base"]}}