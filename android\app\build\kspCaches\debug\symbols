{"build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_quickspender_android_ui_screens_auth_AuthViewModel_HiltModules_BindsModule.java": ["<init>:hilt_aggregated_deps._com_quickspender_android_ui_screens_auth_AuthViewModel_HiltModules_BindsModule", "_com_quickspender_android_ui_screens_auth_AuthViewModel_HiltModules_BindsModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\quickspender\\android\\ui\\screens\\transactions\\TransactionListViewModel_HiltModules_KeyModule_ProvideFactory.java": ["<init>:com.quickspender.android.ui.screens.transactions.TransactionListViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "<init>:com.quickspender.android.ui.screens.transactions.TransactionListViewModel_HiltModules_KeyModule_ProvideFactory", "TransactionListViewModel_HiltModules_KeyModule_ProvideFactory:com.quickspender.android.ui.screens.transactions", "provide:com.quickspender.android.ui.screens.transactions.TransactionListViewModel_HiltModules_KeyModule_ProvideFactory", "create:com.quickspender.android.ui.screens.transactions.TransactionListViewModel_HiltModules_KeyModule_ProvideFactory", "get:com.quickspender.android.ui.screens.transactions.TransactionListViewModel_HiltModules_KeyModule_ProvideFactory"], "src\\main\\java\\com\\quickspender\\android\\data\\model\\Category.kt": ["description:com.quickspender.android.data.model.CreateCategoryRequest", "description:com.quickspender.android.data.model.UpdateCategoryRequest", "isDefault:com.quickspender.android.data.model.Category", "description:com.quickspender.android.data.model.Category", "UpdateCategoryRequest:com.quickspender.android.data.model", "value:com.quickspender.android.data.model.CategoryType", "Category:com.quickspender.android.data.model", "EXPENSE:com.quickspender.android.data.model.CategoryType", "color:com.quickspender.android.data.model.Category", "name:com.quickspender.android.data.model.UpdateCategoryRequest", "<init>:com.quickspender.android.data.model.CategoryType.Companion", "<init>:com.quickspender.android.data.model.CategoryType.INCOME", "name:com.quickspender.android.data.model.Category", "id:com.quickspender.android.data.model.Category", "CreateCategoryRequest:com.quickspender.android.data.model", "Companion:com.quickspender.android.data.model.CategoryType", "color:com.quickspender.android.data.model.CreateCategoryRequest", "createdAt:com.quickspender.android.data.model.Category", "fromValue:com.quickspender.android.data.model.CategoryType.Companion", "type:com.quickspender.android.data.model.UpdateCategoryRequest", "<init>:com.quickspender.android.data.model.CategoryType.EXPENSE", "type:com.quickspender.android.data.model.CreateCategoryRequest", "type:com.quickspender.android.data.model.Category", "icon:com.quickspender.android.data.model.UpdateCategoryRequest", "icon:com.quickspender.android.data.model.Category", "CategoryType:com.quickspender.android.data.model", "INCOME:com.quickspender.android.data.model.CategoryType", "name:com.quickspender.android.data.model.CreateCategoryRequest", "color:com.quickspender.android.data.model.UpdateCategoryRequest", "icon:com.quickspender.android.data.model.CreateCategoryRequest"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\data\\repository\\TransactionRepository_Factory.java": ["TransactionRepository_Factory:com.quickspender.android.data.repository", "get:com.quickspender.android.data.repository.TransactionRepository_Factory", "create:com.quickspender.android.data.repository.TransactionRepository_Factory", "newInstance:com.quickspender.android.data.repository.TransactionRepository_Factory", "<init>:com.quickspender.android.data.repository.TransactionRepository_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_quickspender_android_ui_screens_dashboard_DashboardViewModel_HiltModules_BindsModule.java": ["_com_quickspender_android_ui_screens_dashboard_DashboardViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_quickspender_android_ui_screens_dashboard_DashboardViewModel_HiltModules_BindsModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\ui\\screens\\dashboard\\DashboardViewModel_Factory.java": ["<init>:com.quickspender.android.ui.screens.dashboard.DashboardViewModel_Factory", "DashboardViewModel_Factory:com.quickspender.android.ui.screens.dashboard", "create:com.quickspender.android.ui.screens.dashboard.DashboardViewModel_Factory", "get:com.quickspender.android.ui.screens.dashboard.DashboardViewModel_Factory", "newInstance:com.quickspender.android.ui.screens.dashboard.DashboardViewModel_Factory"], "src\\main\\java\\com\\quickspender\\android\\data\\local\\QuickSpenderDatabase.kt": ["categoryDao:com.quickspender.android.data.local.QuickSpenderDatabase", "transactionDao:com.quickspender.android.data.local.QuickSpenderDatabase", "getDatabase:com.quickspender.android.data.local.QuickSpenderDatabase.Companion", "<init>:com.quickspender.android.data.local.QuickSpenderDatabase", "QuickSpenderDatabase:com.quickspender.android.data.local", "<init>:com.quickspender.android.data.local.QuickSpenderDatabase.Companion", "Companion:com.quickspender.android.data.local.QuickSpenderDatabase"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\di\\DatabaseModule_ProvideTransactionDaoFactory.java": ["get:com.quickspender.android.di.DatabaseModule_ProvideTransactionDaoFactory", "provideTransactionDao:com.quickspender.android.di.DatabaseModule_ProvideTransactionDaoFactory", "<init>:com.quickspender.android.di.DatabaseModule_ProvideTransactionDaoFactory", "DatabaseModule_ProvideTransactionDaoFactory:com.quickspender.android.di", "create:com.quickspender.android.di.DatabaseModule_ProvideTransactionDaoFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\di\\DatabaseModule_ProvideQuickSpenderDatabaseFactory.java": ["get:com.quickspender.android.di.DatabaseModule_ProvideQuickSpenderDatabaseFactory", "create:com.quickspender.android.di.DatabaseModule_ProvideQuickSpenderDatabaseFactory", "provideQuickSpenderDatabase:com.quickspender.android.di.DatabaseModule_ProvideQuickSpenderDatabaseFactory", "DatabaseModule_ProvideQuickSpenderDatabaseFactory:com.quickspender.android.di", "<init>:com.quickspender.android.di.DatabaseModule_ProvideQuickSpenderDatabaseFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_quickspender_android_ui_screens_transactions_TransactionListViewModel_HiltModules_KeyModule.java": ["_com_quickspender_android_ui_screens_transactions_TransactionListViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_quickspender_android_ui_screens_transactions_TransactionListViewModel_HiltModules_KeyModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\Hilt_MainActivity.java": ["inject:com.quickspender.android.Hilt_MainActivity", "Hilt_MainActivity:com.quickspender.android", "componentManager:com.quickspender.android.Hilt_MainActivity", "createComponentManager:com.quickspender.android.Hilt_MainActivity", "onCreate:com.quickspender.android.Hilt_MainActivity", "<init>:com.quickspender.android.Hilt_MainActivity", "generatedComponent:com.quickspender.android.Hilt_MainActivity", "onDestroy:com.quickspender.android.Hilt_MainActivity", "getDefaultViewModelProviderFactory:com.quickspender.android.Hilt_MainActivity"], "src\\main\\java\\com\\quickspender\\android\\data\\repository\\AuthRepository.kt": ["register:com.quickspender.android.data.repository.AuthRepository", "AuthRepository:com.quickspender.android.data.repository", "isLoggedIn:com.quickspender.android.data.repository.AuthRepository", "currentUser:com.quickspender.android.data.repository.AuthRepository", "logout:com.quickspender.android.data.repository.AuthRepository", "authToken:com.quickspender.android.data.repository.AuthRepository", "login:com.quickspender.android.data.repository.AuthRepository"], "src\\main\\java\\com\\quickspender\\android\\data\\api\\TransactionApi.kt": ["createTransaction:com.quickspender.android.data.api.TransactionApi", "TransactionApi:com.quickspender.android.data.api", "deleteTransaction:com.quickspender.android.data.api.TransactionApi", "getTransactions:com.quickspender.android.data.api.TransactionApi", "updateTransaction:com.quickspender.android.data.api.TransactionApi"], "src\\main\\java\\com\\quickspender\\android\\ui\\screens\\main\\MainScreen.kt": ["BottomNavItem:com.quickspender.android.ui.screens.main", "Transactions:com.quickspender.android.ui.screens.main.BottomNavItem", "title:com.quickspender.android.ui.screens.main.BottomNavItem", "route:com.quickspender.android.ui.screens.main.BottomNavItem", "<init>:com.quickspender.android.ui.screens.main.BottomNavItem.Dashboard", "Profile:com.quickspender.android.ui.screens.main.BottomNavItem", "MainScreen:com.quickspender.android.ui.screens.main", "AddTransaction:com.quickspender.android.ui.screens.main.BottomNavItem", "Dashboard:com.quickspender.android.ui.screens.main.BottomNavItem", "<init>:com.quickspender.android.ui.screens.main.BottomNavItem.AddTransaction", "<init>:com.quickspender.android.ui.screens.main.BottomNavItem.Transactions", "icon:com.quickspender.android.ui.screens.main.BottomNavItem", "<init>:com.quickspender.android.ui.screens.main.BottomNavItem.Profile"], "src\\main\\java\\com\\quickspender\\android\\data\\api\\ReportApi.kt": ["ReportApi:com.quickspender.android.data.api", "getSummary:com.quickspender.android.data.api.ReportApi"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\ui\\screens\\auth\\AuthViewModel_Factory.java": ["get:com.quickspender.android.ui.screens.auth.AuthViewModel_Factory", "create:com.quickspender.android.ui.screens.auth.AuthViewModel_Factory", "newInstance:com.quickspender.android.ui.screens.auth.AuthViewModel_Factory", "<init>:com.quickspender.android.ui.screens.auth.AuthViewModel_Factory", "AuthViewModel_Factory:com.quickspender.android.ui.screens.auth"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\data\\repository\\AuthRepository_Factory.java": ["<init>:com.quickspender.android.data.repository.AuthRepository_Factory", "create:com.quickspender.android.data.repository.AuthRepository_Factory", "AuthRepository_Factory:com.quickspender.android.data.repository", "newInstance:com.quickspender.android.data.repository.AuthRepository_Factory", "get:com.quickspender.android.data.repository.AuthRepository_Factory"], "src\\main\\java\\com\\quickspender\\android\\data\\local\\dao\\TransactionDao.kt": ["deleteTransactionById:com.quickspender.android.data.local.dao.TransactionDao", "deleteAllTransactions:com.quickspender.android.data.local.dao.TransactionDao", "insertTransactions:com.quickspender.android.data.local.dao.TransactionDao", "updateTransaction:com.quickspender.android.data.local.dao.TransactionDao", "TransactionDao:com.quickspender.android.data.local.dao", "insertTransaction:com.quickspender.android.data.local.dao.TransactionDao", "getAllTransactions:com.quickspender.android.data.local.dao.TransactionDao", "getTransactionsByCategory:com.quickspender.android.data.local.dao.TransactionDao", "getTransactionById:com.quickspender.android.data.local.dao.TransactionDao", "deleteTransaction:com.quickspender.android.data.local.dao.TransactionDao", "getTransactionsByDateRange:com.quickspender.android.data.local.dao.TransactionDao", "getTransactionsByType:com.quickspender.android.data.local.dao.TransactionDao"], "src\\main\\java\\com\\quickspender\\android\\ui\\screens\\dashboard\\DashboardViewModel.kt": ["uiState:com.quickspender.android.ui.screens.dashboard.DashboardViewModel", "totalExpenses:com.quickspender.android.ui.screens.dashboard.DashboardUiState", "recentTransactions:com.quickspender.android.ui.screens.dashboard.DashboardUiState", "error:com.quickspender.android.ui.screens.dashboard.DashboardUiState", "loadDashboardData:com.quickspender.android.ui.screens.dashboard.DashboardViewModel", "balance:com.quickspender.android.ui.screens.dashboard.DashboardUiState", "expensesByCategory:com.quickspender.android.ui.screens.dashboard.DashboardUiState", "DashboardUiState:com.quickspender.android.ui.screens.dashboard", "isLoading:com.quickspender.android.ui.screens.dashboard.DashboardUiState", "DashboardViewModel:com.quickspender.android.ui.screens.dashboard", "totalIncome:com.quickspender.android.ui.screens.dashboard.DashboardUiState", "incomeByCategory:com.quickspender.android.ui.screens.dashboard.DashboardUiState"], "src\\main\\java\\com\\quickspender\\android\\ui\\screens\\auth\\LoginScreen.kt": ["LoginScreen:com.quickspender.android.ui.screens.auth"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\di\\NetworkModule_ProvideReportApiFactory.java": ["create:com.quickspender.android.di.NetworkModule_ProvideReportApiFactory", "provideReportApi:com.quickspender.android.di.NetworkModule_ProvideReportApiFactory", "get:com.quickspender.android.di.NetworkModule_ProvideReportApiFactory", "NetworkModule_ProvideReportApiFactory:com.quickspender.android.di", "<init>:com.quickspender.android.di.NetworkModule_ProvideReportApiFactory"], "src\\main\\java\\com\\quickspender\\android\\ui\\screens\\transactions\\TransactionListViewModel.kt": ["loadTransactions:com.quickspender.android.ui.screens.transactions.TransactionListViewModel", "TransactionListViewModel:com.quickspender.android.ui.screens.transactions", "clearError:com.quickspender.android.ui.screens.transactions.TransactionListViewModel", "TransactionListUiState:com.quickspender.android.ui.screens.transactions", "error:com.quickspender.android.ui.screens.transactions.TransactionListUiState", "deleteTransaction:com.quickspender.android.ui.screens.transactions.TransactionListViewModel", "transactions:com.quickspender.android.ui.screens.transactions.TransactionListUiState", "uiState:com.quickspender.android.ui.screens.transactions.TransactionListViewModel", "isLoading:com.quickspender.android.ui.screens.transactions.TransactionListUiState"], "src\\main\\java\\com\\quickspender\\android\\ui\\screens\\categories\\CategoryManagementScreen.kt": ["AddCategoryDialog:com.quickspender.android.ui.screens.categories", "CategoryItem:com.quickspender.android.ui.screens.categories", "CategoryManagementScreen:com.quickspender.android.ui.screens.categories"], "src\\main\\java\\com\\quickspender\\android\\ui\\components\\charts\\CategoryBarChart.kt": ["CategoryBarChart:com.quickspender.android.ui.components.charts"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_quickspender_android_ui_screens_categories_CategoryManagementViewModel_HiltModules_BindsModule.java": ["_com_quickspender_android_ui_screens_categories_CategoryManagementViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_quickspender_android_ui_screens_categories_CategoryManagementViewModel_HiltModules_BindsModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\quickspender\\android\\ui\\screens\\categories\\CategoryManagementViewModel_HiltModules_KeyModule_ProvideFactory.java": ["<init>:com.quickspender.android.ui.screens.categories.CategoryManagementViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "get:com.quickspender.android.ui.screens.categories.CategoryManagementViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.quickspender.android.ui.screens.categories.CategoryManagementViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.quickspender.android.ui.screens.categories.CategoryManagementViewModel_HiltModules_KeyModule_ProvideFactory", "CategoryManagementViewModel_HiltModules_KeyModule_ProvideFactory:com.quickspender.android.ui.screens.categories", "create:com.quickspender.android.ui.screens.categories.CategoryManagementViewModel_HiltModules_KeyModule_ProvideFactory"], "src\\main\\java\\com\\quickspender\\android\\QuickSpenderApplication.kt": ["QuickSpenderApplication:com.quickspender.android", "<init>:com.quickspender.android.QuickSpenderApplication"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_quickspender_android_di_DatabaseModule.java": ["_com_quickspender_android_di_DatabaseModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_quickspender_android_di_DatabaseModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\di\\NetworkModule_ProvideRetrofitFactory.java": ["create:com.quickspender.android.di.NetworkModule_ProvideRetrofitFactory", "get:com.quickspender.android.di.NetworkModule_ProvideRetrofitFactory", "NetworkModule_ProvideRetrofitFactory:com.quickspender.android.di", "provideRetrofit:com.quickspender.android.di.NetworkModule_ProvideRetrofitFactory", "<init>:com.quickspender.android.di.NetworkModule_ProvideRetrofitFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\ui\\screens\\categories\\CategoryManagementViewModel_HiltModules.java": ["binds:com.quickspender.android.ui.screens.categories.CategoryManagementViewModel_HiltModules.BindsModule", "KeyModule:com.quickspender.android.ui.screens.categories.CategoryManagementViewModel_HiltModules", "provide:com.quickspender.android.ui.screens.categories.CategoryManagementViewModel_HiltModules.KeyModule", "BindsModule:com.quickspender.android.ui.screens.categories.CategoryManagementViewModel_HiltModules", "CategoryManagementViewModel_HiltModules:com.quickspender.android.ui.screens.categories"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\data\\repository\\ReportRepository_Factory.java": ["create:com.quickspender.android.data.repository.ReportRepository_Factory", "ReportRepository_Factory:com.quickspender.android.data.repository", "<init>:com.quickspender.android.data.repository.ReportRepository_Factory", "newInstance:com.quickspender.android.data.repository.ReportRepository_Factory", "get:com.quickspender.android.data.repository.ReportRepository_Factory"], "src\\main\\java\\com\\quickspender\\android\\ui\\screens\\profile\\ProfileScreen.kt": ["ProfileScreen:com.quickspender.android.ui.screens.profile"], "src\\main\\java\\com\\quickspender\\android\\ui\\screens\\dashboard\\DashboardScreen.kt": ["SummaryCard:com.quickspender.android.ui.screens.dashboard", "DashboardScreen:com.quickspender.android.ui.screens.dashboard"], "src\\main\\java\\com\\quickspender\\android\\ui\\screens\\transactions\\AddTransactionViewModel.kt": ["uiState:com.quickspender.android.ui.screens.transactions.AddTransactionViewModel", "AddTransactionUiState:com.quickspender.android.ui.screens.transactions", "transactionAdded:com.quickspender.android.ui.screens.transactions.AddTransactionUiState", "error:com.quickspender.android.ui.screens.transactions.AddTransactionUiState", "AddTransactionViewModel:com.quickspender.android.ui.screens.transactions", "clearError:com.quickspender.android.ui.screens.transactions.AddTransactionViewModel", "loadCategories:com.quickspender.android.ui.screens.transactions.AddTransactionViewModel", "isLoading:com.quickspender.android.ui.screens.transactions.AddTransactionUiState", "categories:com.quickspender.android.ui.screens.transactions.AddTransactionUiState", "addTransaction:com.quickspender.android.ui.screens.transactions.AddTransactionViewModel"], "src\\main\\java\\com\\quickspender\\android\\ui\\screens\\dashboard\\TransactionItem.kt": ["TransactionItem:com.quickspender.android.ui.screens.dashboard"], "src\\main\\java\\com\\quickspender\\android\\data\\model\\Transaction.kt": ["EXPENSE:com.quickspender.android.data.model.TransactionType", "amount:com.quickspender.android.data.model.UpdateTransactionRequest", "notes:com.quickspender.android.data.model.UpdateTransactionRequest", "TransactionType:com.quickspender.android.data.model", "amount:com.quickspender.android.data.model.Transaction", "id:com.quickspender.android.data.model.TransactionResponse", "transactionDate:com.quickspender.android.data.model.TransactionResponse", "createdAt:com.quickspender.android.data.model.TransactionResponse", "categoryId:com.quickspender.android.data.model.CreateTransactionRequest", "transaction:com.quickspender.android.data.model.TransactionWithCategory", "transactionDate:com.quickspender.android.data.model.Transaction", "description:com.quickspender.android.data.model.TransactionResponse", "category:com.quickspender.android.data.model.TransactionWithCategory", "CreateTransactionRequest:com.quickspender.android.data.model", "description:com.quickspender.android.data.model.UpdateTransactionRequest", "<init>:com.quickspender.android.data.model.TransactionType.INCOME", "UpdateTransactionRequest:com.quickspender.android.data.model", "notes:com.quickspender.android.data.model.Transaction", "description:com.quickspender.android.data.model.CreateTransactionRequest", "<init>:com.quickspender.android.data.model.TransactionType.Companion", "fromValue:com.quickspender.android.data.model.TransactionType.Companion", "type:com.quickspender.android.data.model.Transaction", "amount:com.quickspender.android.data.model.TransactionResponse", "notes:com.quickspender.android.data.model.CreateTransactionRequest", "Transaction:com.quickspender.android.data.model", "amount:com.quickspender.android.data.model.CreateTransactionRequest", "notes:com.quickspender.android.data.model.TransactionResponse", "categoryId:com.quickspender.android.data.model.UpdateTransactionRequest", "INCOME:com.quickspender.android.data.model.TransactionType", "categoryId:com.quickspender.android.data.model.Transaction", "TransactionResponse:com.quickspender.android.data.model", "type:com.quickspender.android.data.model.UpdateTransactionRequest", "<init>:com.quickspender.android.data.model.TransactionType.EXPENSE", "type:com.quickspender.android.data.model.TransactionResponse", "category:com.quickspender.android.data.model.TransactionResponse", "transactionDate:com.quickspender.android.data.model.CreateTransactionRequest", "value:com.quickspender.android.data.model.TransactionType", "id:com.quickspender.android.data.model.Transaction", "createdAt:com.quickspender.android.data.model.Transaction", "TransactionWithCategory:com.quickspender.android.data.model", "Companion:com.quickspender.android.data.model.TransactionType", "description:com.quickspender.android.data.model.Transaction", "type:com.quickspender.android.data.model.CreateTransactionRequest", "transactionDate:com.quickspender.android.data.model.UpdateTransactionRequest"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\dagger\\hilt\\internal\\aggregatedroot\\codegen\\_com_quickspender_android_QuickSpenderApplication.java": ["<init>:dagger.hilt.internal.aggregatedroot.codegen._com_quickspender_android_QuickSpenderApplication", "_com_quickspender_android_QuickSpenderApplication:dagger.hilt.internal.aggregatedroot.codegen"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\di\\NetworkModule_ProvideGsonFactory.java": ["provideGson:com.quickspender.android.di.NetworkModule_ProvideGsonFactory", "get:com.quickspender.android.di.NetworkModule_ProvideGsonFactory", "<init>:com.quickspender.android.di.NetworkModule_ProvideGsonFactory.InstanceHolder", "NetworkModule_ProvideGsonFactory:com.quickspender.android.di", "<init>:com.quickspender.android.di.NetworkModule_ProvideGsonFactory", "create:com.quickspender.android.di.NetworkModule_ProvideGsonFactory"], "src\\main\\java\\com\\quickspender\\android\\data\\model\\User.kt": ["lastName:com.quickspender.android.data.model.User", "email:com.quickspender.android.data.model.RegisterRequest", "password:com.quickspender.android.data.model.RegisterRequest", "firstName:com.quickspender.android.data.model.AuthenticationResponse", "lastName:com.quickspender.android.data.model.AuthenticationResponse", "LoginRequest:com.quickspender.android.data.model", "RegisterRequest:com.quickspender.android.data.model", "firstName:com.quickspender.android.data.model.User", "lastName:com.quickspender.android.data.model.RegisterRequest", "User:com.quickspender.android.data.model", "email:com.quickspender.android.data.model.AuthenticationResponse", "token:com.quickspender.android.data.model.AuthenticationResponse", "id:com.quickspender.android.data.model.AuthenticationResponse", "email:com.quickspender.android.data.model.LoginRequest", "AuthenticationResponse:com.quickspender.android.data.model", "email:com.quickspender.android.data.model.User", "firstName:com.quickspender.android.data.model.RegisterRequest", "id:com.quickspender.android.data.model.User", "password:com.quickspender.android.data.model.LoginRequest"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\ui\\screens\\categories\\CategoryManagementViewModel_Factory.java": ["<init>:com.quickspender.android.ui.screens.categories.CategoryManagementViewModel_Factory", "get:com.quickspender.android.ui.screens.categories.CategoryManagementViewModel_Factory", "CategoryManagementViewModel_Factory:com.quickspender.android.ui.screens.categories", "create:com.quickspender.android.ui.screens.categories.CategoryManagementViewModel_Factory", "newInstance:com.quickspender.android.ui.screens.categories.CategoryManagementViewModel_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\ui\\screens\\auth\\AuthViewModel_HiltModules.java": ["AuthViewModel_HiltModules:com.quickspender.android.ui.screens.auth", "KeyModule:com.quickspender.android.ui.screens.auth.AuthViewModel_HiltModules", "binds:com.quickspender.android.ui.screens.auth.AuthViewModel_HiltModules.BindsModule", "BindsModule:com.quickspender.android.ui.screens.auth.AuthViewModel_HiltModules", "provide:com.quickspender.android.ui.screens.auth.AuthViewModel_HiltModules.KeyModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\di\\NetworkModule_ProvideAuthInterceptorFactory.java": ["provideAuthInterceptor:com.quickspender.android.di.NetworkModule_ProvideAuthInterceptorFactory", "<init>:com.quickspender.android.di.NetworkModule_ProvideAuthInterceptorFactory", "create:com.quickspender.android.di.NetworkModule_ProvideAuthInterceptorFactory", "NetworkModule_ProvideAuthInterceptorFactory:com.quickspender.android.di", "get:com.quickspender.android.di.NetworkModule_ProvideAuthInterceptorFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\di\\NetworkModule_ProvideTransactionApiFactory.java": ["create:com.quickspender.android.di.NetworkModule_ProvideTransactionApiFactory", "NetworkModule_ProvideTransactionApiFactory:com.quickspender.android.di", "<init>:com.quickspender.android.di.NetworkModule_ProvideTransactionApiFactory", "get:com.quickspender.android.di.NetworkModule_ProvideTransactionApiFactory", "provideTransactionApi:com.quickspender.android.di.NetworkModule_ProvideTransactionApiFactory"], "src\\main\\java\\com\\quickspender\\android\\MainActivity.kt": ["MainActivity:com.quickspender.android", "<init>:com.quickspender.android.MainActivity", "onCreate:com.quickspender.android.MainActivity"], "src\\main\\java\\com\\quickspender\\android\\data\\preferences\\UserPreferences.kt": ["isLoggedIn:com.quickspender.android.data.preferences.UserPreferences", "authToken:com.quickspender.android.data.preferences.UserPreferences", "clearAuthData:com.quickspender.android.data.preferences.UserPreferences", "saveAuthData:com.quickspender.android.data.preferences.UserPreferences", "IS_LOGGED_IN:com.quickspender.android.data.preferences.UserPreferences.PreferencesKeys", "currentUser:com.quickspender.android.data.preferences.UserPreferences", "CURRENT_USER:com.quickspender.android.data.preferences.UserPreferences.PreferencesKeys", "UserPreferences:com.quickspender.android.data.preferences", "AUTH_TOKEN:com.quickspender.android.data.preferences.UserPreferences.PreferencesKeys", "<init>:com.quickspender.android.data.preferences.UserPreferences.PreferencesKeys"], "src\\main\\java\\com\\quickspender\\android\\ui\\screens\\transactions\\AddTransactionScreen.kt": ["AddTransactionScreen:com.quickspender.android.ui.screens.transactions"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\data\\preferences\\UserPreferences_Factory.java": ["get:com.quickspender.android.data.preferences.UserPreferences_Factory", "newInstance:com.quickspender.android.data.preferences.UserPreferences_Factory", "UserPreferences_Factory:com.quickspender.android.data.preferences", "<init>:com.quickspender.android.data.preferences.UserPreferences_Factory", "create:com.quickspender.android.data.preferences.UserPreferences_Factory"], "src\\main\\java\\com\\quickspender\\android\\ui\\screens\\transactions\\TransactionListScreen.kt": ["TransactionListScreen:com.quickspender.android.ui.screens.transactions"], "src\\main\\java\\com\\quickspender\\android\\ui\\screens\\auth\\RegisterScreen.kt": ["RegisterScreen:com.quickspender.android.ui.screens.auth"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\ui\\screens\\transactions\\TransactionListViewModel_Factory.java": ["create:com.quickspender.android.ui.screens.transactions.TransactionListViewModel_Factory", "get:com.quickspender.android.ui.screens.transactions.TransactionListViewModel_Factory", "TransactionListViewModel_Factory:com.quickspender.android.ui.screens.transactions", "newInstance:com.quickspender.android.ui.screens.transactions.TransactionListViewModel_Factory", "<init>:com.quickspender.android.ui.screens.transactions.TransactionListViewModel_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\data\\local\\dao\\CategoryDao_Impl.java": ["getCategoryById:com.quickspender.android.data.local.dao.CategoryDao_Impl", "deleteCategory:com.quickspender.android.data.local.dao.CategoryDao_Impl", "deleteAllCategories:com.quickspender.android.data.local.dao.CategoryDao_Impl", "<init>:com.quickspender.android.data.local.dao.CategoryDao_Impl", "getCategoriesByType:com.quickspender.android.data.local.dao.CategoryDao_Impl", "getAllCategories:com.quickspender.android.data.local.dao.CategoryDao_Impl", "deleteCategoryById:com.quickspender.android.data.local.dao.CategoryDao_Impl", "CategoryDao_Impl:com.quickspender.android.data.local.dao", "getRequiredConverters:com.quickspender.android.data.local.dao.CategoryDao_Impl", "insertCategory:com.quickspender.android.data.local.dao.CategoryDao_Impl", "insertCategories:com.quickspender.android.data.local.dao.CategoryDao_Impl", "updateCategory:com.quickspender.android.data.local.dao.CategoryDao_Impl"], "src\\main\\java\\com\\quickspender\\android\\data\\repository\\ReportRepository.kt": ["ReportRepository:com.quickspender.android.data.repository", "getSummary:com.quickspender.android.data.repository.ReportRepository"], "src\\main\\java\\com\\quickspender\\android\\ui\\components\\charts\\CategoryPieChart.kt": ["CategoryPieChart:com.quickspender.android.ui.components.charts", "CategoryLegendItem:com.quickspender.android.ui.components.charts"], "src\\main\\java\\com\\quickspender\\android\\data\\repository\\CategoryRepository.kt": ["getCategories:com.quickspender.android.data.repository.CategoryRepository", "createCategory:com.quickspender.android.data.repository.CategoryRepository", "getLocalCategories:com.quickspender.android.data.repository.CategoryRepository", "getLocalCategoriesByType:com.quickspender.android.data.repository.CategoryRepository", "deleteCategory:com.quickspender.android.data.repository.CategoryRepository", "CategoryRepository:com.quickspender.android.data.repository"], "src\\main\\java\\com\\quickspender\\android\\ui\\screens\\auth\\AuthViewModel.kt": ["isLoggedIn:com.quickspender.android.ui.screens.auth.AuthViewModel", "logout:com.quickspender.android.ui.screens.auth.AuthViewModel", "AuthViewModel:com.quickspender.android.ui.screens.auth", "AuthUiState:com.quickspender.android.ui.screens.auth", "login:com.quickspender.android.ui.screens.auth.AuthViewModel", "currentUser:com.quickspender.android.ui.screens.auth.AuthViewModel", "uiState:com.quickspender.android.ui.screens.auth.AuthViewModel", "isLoading:com.quickspender.android.ui.screens.auth.AuthUiState", "registerSuccess:com.quickspender.android.ui.screens.auth.AuthUiState", "clearError:com.quickspender.android.ui.screens.auth.AuthViewModel", "clearSuccessStates:com.quickspender.android.ui.screens.auth.AuthViewModel", "loginSuccess:com.quickspender.android.ui.screens.auth.AuthUiState", "error:com.quickspender.android.ui.screens.auth.AuthUiState", "register:com.quickspender.android.ui.screens.auth.AuthViewModel"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\quickspender\\android\\ui\\screens\\dashboard\\DashboardViewModel_HiltModules_KeyModule_ProvideFactory.java": ["<init>:com.quickspender.android.ui.screens.dashboard.DashboardViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "<init>:com.quickspender.android.ui.screens.dashboard.DashboardViewModel_HiltModules_KeyModule_ProvideFactory", "DashboardViewModel_HiltModules_KeyModule_ProvideFactory:com.quickspender.android.ui.screens.dashboard", "get:com.quickspender.android.ui.screens.dashboard.DashboardViewModel_HiltModules_KeyModule_ProvideFactory", "create:com.quickspender.android.ui.screens.dashboard.DashboardViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.quickspender.android.ui.screens.dashboard.DashboardViewModel_HiltModules_KeyModule_ProvideFactory"], "src\\main\\java\\com\\quickspender\\android\\di\\NetworkModule.kt": ["provideOkHttpClient:com.quickspender.android.di.NetworkModule", "provideCategoryApi:com.quickspender.android.di.NetworkModule", "NetworkModule:com.quickspender.android.di", "provideGson:com.quickspender.android.di.NetworkModule", "provideAuthInterceptor:com.quickspender.android.di.NetworkModule", "provideRetrofit:com.quickspender.android.di.NetworkModule", "provideTransactionApi:com.quickspender.android.di.NetworkModule", "provideReportApi:com.quickspender.android.di.NetworkModule", "<init>:com.quickspender.android.di.NetworkModule", "provideAuthApi:com.quickspender.android.di.NetworkModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\ui\\screens\\dashboard\\DashboardViewModel_HiltModules.java": ["BindsModule:com.quickspender.android.ui.screens.dashboard.DashboardViewModel_HiltModules", "DashboardViewModel_HiltModules:com.quickspender.android.ui.screens.dashboard", "binds:com.quickspender.android.ui.screens.dashboard.DashboardViewModel_HiltModules.BindsModule", "KeyModule:com.quickspender.android.ui.screens.dashboard.DashboardViewModel_HiltModules", "provide:com.quickspender.android.ui.screens.dashboard.DashboardViewModel_HiltModules.KeyModule"], "src\\main\\java\\com\\quickspender\\android\\data\\model\\Report.kt": ["totalAmount:com.quickspender.android.data.model.CategorySummaryResponse", "toDate:com.quickspender.android.data.model.SummaryResponse", "balance:com.quickspender.android.data.model.SummaryResponse", "expensesByCategory:com.quickspender.android.data.model.SummaryResponse", "CategorySummaryResponse:com.quickspender.android.data.model", "totalExpenses:com.quickspender.android.data.model.SummaryResponse", "SummaryResponse:com.quickspender.android.data.model", "category:com.quickspender.android.data.model.CategorySummaryResponse", "totalIncome:com.quickspender.android.data.model.SummaryResponse", "fromDate:com.quickspender.android.data.model.SummaryResponse", "percentage:com.quickspender.android.data.model.CategorySummaryResponse", "incomeByCategory:com.quickspender.android.data.model.SummaryResponse", "transactionCount:com.quickspender.android.data.model.CategorySummaryResponse"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_quickspender_android_ui_screens_transactions_TransactionListViewModel_HiltModules_BindsModule.java": ["_com_quickspender_android_ui_screens_transactions_TransactionListViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_quickspender_android_ui_screens_transactions_TransactionListViewModel_HiltModules_BindsModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_quickspender_android_ui_screens_transactions_AddTransactionViewModel_HiltModules_KeyModule.java": ["<init>:hilt_aggregated_deps._com_quickspender_android_ui_screens_transactions_AddTransactionViewModel_HiltModules_KeyModule", "_com_quickspender_android_ui_screens_transactions_AddTransactionViewModel_HiltModules_KeyModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\data\\local\\QuickSpenderDatabase_Impl.java": ["QuickSpenderDatabase_Impl:com.quickspender.android.data.local", "createOpenHelper:com.quickspender.android.data.local.QuickSpenderDatabase_Impl", "getRequiredTypeConverters:com.quickspender.android.data.local.QuickSpenderDatabase_Impl", "createInvalidationTracker:com.quickspender.android.data.local.QuickSpenderDatabase_Impl", "getRequiredAutoMigrationSpecs:com.quickspender.android.data.local.QuickSpenderDatabase_Impl", "clearAllTables:com.quickspender.android.data.local.QuickSpenderDatabase_Impl", "categoryDao:com.quickspender.android.data.local.QuickSpenderDatabase_Impl", "transactionDao:com.quickspender.android.data.local.QuickSpenderDatabase_Impl", "getAutoMigrations:com.quickspender.android.data.local.QuickSpenderDatabase_Impl", "<init>:com.quickspender.android.data.local.QuickSpenderDatabase_Impl"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\QuickSpenderApplication_GeneratedInjector.java": ["QuickSpenderApplication_GeneratedInjector:com.quickspender.android", "injectQuickSpenderApplication:com.quickspender.android.QuickSpenderApplication_GeneratedInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\ui\\screens\\transactions\\TransactionListViewModel_HiltModules.java": ["TransactionListViewModel_HiltModules:com.quickspender.android.ui.screens.transactions", "BindsModule:com.quickspender.android.ui.screens.transactions.TransactionListViewModel_HiltModules", "KeyModule:com.quickspender.android.ui.screens.transactions.TransactionListViewModel_HiltModules", "binds:com.quickspender.android.ui.screens.transactions.TransactionListViewModel_HiltModules.BindsModule", "provide:com.quickspender.android.ui.screens.transactions.TransactionListViewModel_HiltModules.KeyModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\data\\repository\\CategoryRepository_Factory.java": ["CategoryRepository_Factory:com.quickspender.android.data.repository", "get:com.quickspender.android.data.repository.CategoryRepository_Factory", "<init>:com.quickspender.android.data.repository.CategoryRepository_Factory", "newInstance:com.quickspender.android.data.repository.CategoryRepository_Factory", "create:com.quickspender.android.data.repository.CategoryRepository_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_quickspender_android_MainActivity_GeneratedInjector.java": ["_com_quickspender_android_MainActivity_GeneratedInjector:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_quickspender_android_MainActivity_GeneratedInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\di\\DatabaseModule_ProvideCategoryDaoFactory.java": ["provideCategoryDao:com.quickspender.android.di.DatabaseModule_ProvideCategoryDaoFactory", "<init>:com.quickspender.android.di.DatabaseModule_ProvideCategoryDaoFactory", "DatabaseModule_ProvideCategoryDaoFactory:com.quickspender.android.di", "create:com.quickspender.android.di.DatabaseModule_ProvideCategoryDaoFactory", "get:com.quickspender.android.di.DatabaseModule_ProvideCategoryDaoFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\di\\NetworkModule_ProvideAuthApiFactory.java": ["get:com.quickspender.android.di.NetworkModule_ProvideAuthApiFactory", "<init>:com.quickspender.android.di.NetworkModule_ProvideAuthApiFactory", "create:com.quickspender.android.di.NetworkModule_ProvideAuthApiFactory", "NetworkModule_ProvideAuthApiFactory:com.quickspender.android.di", "provideAuthApi:com.quickspender.android.di.NetworkModule_ProvideAuthApiFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_quickspender_android_ui_screens_auth_AuthViewModel_HiltModules_KeyModule.java": ["<init>:hilt_aggregated_deps._com_quickspender_android_ui_screens_auth_AuthViewModel_HiltModules_KeyModule", "_com_quickspender_android_ui_screens_auth_AuthViewModel_HiltModules_KeyModule:hilt_aggregated_deps"], "src\\main\\java\\com\\quickspender\\android\\di\\DatabaseModule.kt": ["<init>:com.quickspender.android.di.DatabaseModule", "provideTransactionDao:com.quickspender.android.di.DatabaseModule", "provideQuickSpenderDatabase:com.quickspender.android.di.DatabaseModule", "provideCategoryDao:com.quickspender.android.di.DatabaseModule", "DatabaseModule:com.quickspender.android.di"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_quickspender_android_ui_screens_transactions_AddTransactionViewModel_HiltModules_BindsModule.java": ["_com_quickspender_android_ui_screens_transactions_AddTransactionViewModel_HiltModules_BindsModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_quickspender_android_ui_screens_transactions_AddTransactionViewModel_HiltModules_BindsModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\quickspender\\android\\ui\\screens\\auth\\AuthViewModel_HiltModules_KeyModule_ProvideFactory.java": ["create:com.quickspender.android.ui.screens.auth.AuthViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.quickspender.android.ui.screens.auth.AuthViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "provide:com.quickspender.android.ui.screens.auth.AuthViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.quickspender.android.ui.screens.auth.AuthViewModel_HiltModules_KeyModule_ProvideFactory", "get:com.quickspender.android.ui.screens.auth.AuthViewModel_HiltModules_KeyModule_ProvideFactory", "AuthViewModel_HiltModules_KeyModule_ProvideFactory:com.quickspender.android.ui.screens.auth"], "src\\main\\java\\com\\quickspender\\android\\ui\\theme\\Type.kt": ["Typography:com.quickspender.android.ui.theme"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\MainActivity_GeneratedInjector.java": ["injectMainActivity:com.quickspender.android.MainActivity_GeneratedInjector", "MainActivity_GeneratedInjector:com.quickspender.android"], "src\\main\\java\\com\\quickspender\\android\\ui\\theme\\Theme.kt": ["QuickSpenderTheme:com.quickspender.android.ui.theme"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\hilt_aggregated_deps\\_com_quickspender_android_di_NetworkModule.java": ["_com_quickspender_android_di_NetworkModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_quickspender_android_di_NetworkModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\ui\\screens\\transactions\\AddTransactionViewModel_Factory.java": ["AddTransactionViewModel_Factory:com.quickspender.android.ui.screens.transactions", "create:com.quickspender.android.ui.screens.transactions.AddTransactionViewModel_Factory", "newInstance:com.quickspender.android.ui.screens.transactions.AddTransactionViewModel_Factory", "<init>:com.quickspender.android.ui.screens.transactions.AddTransactionViewModel_Factory", "get:com.quickspender.android.ui.screens.transactions.AddTransactionViewModel_Factory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\di\\NetworkModule_ProvideCategoryApiFactory.java": ["<init>:com.quickspender.android.di.NetworkModule_ProvideCategoryApiFactory", "create:com.quickspender.android.di.NetworkModule_ProvideCategoryApiFactory", "NetworkModule_ProvideCategoryApiFactory:com.quickspender.android.di", "provideCategoryApi:com.quickspender.android.di.NetworkModule_ProvideCategoryApiFactory", "get:com.quickspender.android.di.NetworkModule_ProvideCategoryApiFactory"], "src\\main\\java\\com\\quickspender\\android\\ui\\navigation\\QuickSpenderNavigation.kt": ["QuickSpenderNavigation:com.quickspender.android.ui.navigation"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\di\\NetworkModule_ProvideOkHttpClientFactory.java": ["<init>:com.quickspender.android.di.NetworkModule_ProvideOkHttpClientFactory", "get:com.quickspender.android.di.NetworkModule_ProvideOkHttpClientFactory", "create:com.quickspender.android.di.NetworkModule_ProvideOkHttpClientFactory", "NetworkModule_ProvideOkHttpClientFactory:com.quickspender.android.di", "provideOkHttpClient:com.quickspender.android.di.NetworkModule_ProvideOkHttpClientFactory"], "src\\main\\java\\com\\quickspender\\android\\data\\api\\CategoryApi.kt": ["CategoryApi:com.quickspender.android.data.api", "deleteCategory:com.quickspender.android.data.api.CategoryApi", "getCategories:com.quickspender.android.data.api.CategoryApi", "createCategory:com.quickspender.android.data.api.CategoryApi", "updateCategory:com.quickspender.android.data.api.CategoryApi"], "src\\main\\java\\com\\quickspender\\android\\data\\api\\AuthApi.kt": ["register:com.quickspender.android.data.api.AuthApi", "AuthApi:com.quickspender.android.data.api", "login:com.quickspender.android.data.api.AuthApi"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_quickspender_android_ui_screens_categories_CategoryManagementViewModel_HiltModules_KeyModule.java": ["_com_quickspender_android_ui_screens_categories_CategoryManagementViewModel_HiltModules_KeyModule:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_quickspender_android_ui_screens_categories_CategoryManagementViewModel_HiltModules_KeyModule"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_quickspender_android_QuickSpenderApplication_GeneratedInjector.java": ["_com_quickspender_android_QuickSpenderApplication_GeneratedInjector:hilt_aggregated_deps", "<init>:hilt_aggregated_deps._com_quickspender_android_QuickSpenderApplication_GeneratedInjector"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\com\\quickspender\\android\\ui\\screens\\transactions\\AddTransactionViewModel_HiltModules_KeyModule_ProvideFactory.java": ["create:com.quickspender.android.ui.screens.transactions.AddTransactionViewModel_HiltModules_KeyModule_ProvideFactory", "provide:com.quickspender.android.ui.screens.transactions.AddTransactionViewModel_HiltModules_KeyModule_ProvideFactory", "AddTransactionViewModel_HiltModules_KeyModule_ProvideFactory:com.quickspender.android.ui.screens.transactions", "<init>:com.quickspender.android.ui.screens.transactions.AddTransactionViewModel_HiltModules_KeyModule_ProvideFactory", "<init>:com.quickspender.android.ui.screens.transactions.AddTransactionViewModel_HiltModules_KeyModule_ProvideFactory.InstanceHolder", "get:com.quickspender.android.ui.screens.transactions.AddTransactionViewModel_HiltModules_KeyModule_ProvideFactory"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\ui\\screens\\transactions\\AddTransactionViewModel_HiltModules.java": ["AddTransactionViewModel_HiltModules:com.quickspender.android.ui.screens.transactions", "BindsModule:com.quickspender.android.ui.screens.transactions.AddTransactionViewModel_HiltModules", "KeyModule:com.quickspender.android.ui.screens.transactions.AddTransactionViewModel_HiltModules", "binds:com.quickspender.android.ui.screens.transactions.AddTransactionViewModel_HiltModules.BindsModule", "provide:com.quickspender.android.ui.screens.transactions.AddTransactionViewModel_HiltModules.KeyModule"], "src\\main\\java\\com\\quickspender\\android\\data\\repository\\TransactionRepository.kt": ["getTransactions:com.quickspender.android.data.repository.TransactionRepository", "deleteTransaction:com.quickspender.android.data.repository.TransactionRepository", "TransactionRepository:com.quickspender.android.data.repository", "getLocalTransactions:com.quickspender.android.data.repository.TransactionRepository", "createTransaction:com.quickspender.android.data.repository.TransactionRepository"], "src\\main\\java\\com\\quickspender\\android\\data\\local\\dao\\CategoryDao.kt": ["CategoryDao:com.quickspender.android.data.local.dao", "deleteCategory:com.quickspender.android.data.local.dao.CategoryDao", "deleteAllCategories:com.quickspender.android.data.local.dao.CategoryDao", "insertCategory:com.quickspender.android.data.local.dao.CategoryDao", "getAllCategories:com.quickspender.android.data.local.dao.CategoryDao", "getCategoriesByType:com.quickspender.android.data.local.dao.CategoryDao", "deleteCategoryById:com.quickspender.android.data.local.dao.CategoryDao", "updateCategory:com.quickspender.android.data.local.dao.CategoryDao", "insertCategories:com.quickspender.android.data.local.dao.CategoryDao", "getCategoryById:com.quickspender.android.data.local.dao.CategoryDao"], "build\\generated\\ksp\\debug\\java\\byRounds\\2\\hilt_aggregated_deps\\_com_quickspender_android_ui_screens_dashboard_DashboardViewModel_HiltModules_KeyModule.java": ["<init>:hilt_aggregated_deps._com_quickspender_android_ui_screens_dashboard_DashboardViewModel_HiltModules_KeyModule", "_com_quickspender_android_ui_screens_dashboard_DashboardViewModel_HiltModules_KeyModule:hilt_aggregated_deps"], "build\\generated\\ksp\\debug\\java\\byRounds\\1\\com\\quickspender\\android\\data\\local\\dao\\TransactionDao_Impl.java": ["getAllTransactions:com.quickspender.android.data.local.dao.TransactionDao_Impl", "getTransactionsByCategory:com.quickspender.android.data.local.dao.TransactionDao_Impl", "insertTransaction:com.quickspender.android.data.local.dao.TransactionDao_Impl", "getRequiredConverters:com.quickspender.android.data.local.dao.TransactionDao_Impl", "deleteTransactionById:com.quickspender.android.data.local.dao.TransactionDao_Impl", "<init>:com.quickspender.android.data.local.dao.TransactionDao_Impl", "getTransactionsByType:com.quickspender.android.data.local.dao.TransactionDao_Impl", "insertTransactions:com.quickspender.android.data.local.dao.TransactionDao_Impl", "deleteTransaction:com.quickspender.android.data.local.dao.TransactionDao_Impl", "updateTransaction:com.quickspender.android.data.local.dao.TransactionDao_Impl", "deleteAllTransactions:com.quickspender.android.data.local.dao.TransactionDao_Impl", "TransactionDao_Impl:com.quickspender.android.data.local.dao", "getTransactionsByDateRange:com.quickspender.android.data.local.dao.TransactionDao_Impl", "getTransactionById:com.quickspender.android.data.local.dao.TransactionDao_Impl"], "src\\main\\java\\com\\quickspender\\android\\ui\\theme\\Color.kt": ["Purple40:com.quickspender.android.ui.theme", "Purple80:com.quickspender.android.ui.theme", "CategoryBlue:com.quickspender.android.ui.theme", "Pink80:com.quickspender.android.ui.theme", "PurpleGrey40:com.quickspender.android.ui.theme", "ExpenseRed:com.quickspender.android.ui.theme", "Pink40:com.quickspender.android.ui.theme", "BackgroundGray:com.quickspender.android.ui.theme", "PurpleGrey80:com.quickspender.android.ui.theme", "IncomeGreen:com.quickspender.android.ui.theme"], "src\\main\\java\\com\\quickspender\\android\\ui\\screens\\categories\\CategoryManagementViewModel.kt": ["uiState:com.quickspender.android.ui.screens.categories.CategoryManagementViewModel", "successMessage:com.quickspender.android.ui.screens.categories.CategoryManagementUiState", "clearError:com.quickspender.android.ui.screens.categories.CategoryManagementViewModel", "CategoryManagementViewModel:com.quickspender.android.ui.screens.categories", "loadCategories:com.quickspender.android.ui.screens.categories.CategoryManagementViewModel", "deleteCategory:com.quickspender.android.ui.screens.categories.CategoryManagementViewModel", "categories:com.quickspender.android.ui.screens.categories.CategoryManagementUiState", "error:com.quickspender.android.ui.screens.categories.CategoryManagementUiState", "clearSuccessMessage:com.quickspender.android.ui.screens.categories.CategoryManagementViewModel", "CategoryManagementUiState:com.quickspender.android.ui.screens.categories", "isLoading:com.quickspender.android.ui.screens.categories.CategoryManagementUiState", "createCategory:com.quickspender.android.ui.screens.categories.CategoryManagementViewModel"]}