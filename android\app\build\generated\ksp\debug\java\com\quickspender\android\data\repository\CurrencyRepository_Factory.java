package com.quickspender.android.data.repository;

import com.quickspender.android.data.preferences.UserPreferences;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class CurrencyRepository_Factory implements Factory<CurrencyRepository> {
  private final Provider<UserPreferences> userPreferencesProvider;

  public CurrencyRepository_Factory(Provider<UserPreferences> userPreferencesProvider) {
    this.userPreferencesProvider = userPreferencesProvider;
  }

  @Override
  public CurrencyRepository get() {
    return newInstance(userPreferencesProvider.get());
  }

  public static CurrencyRepository_Factory create(
      Provider<UserPreferences> userPreferencesProvider) {
    return new CurrencyRepository_Factory(userPreferencesProvider);
  }

  public static CurrencyRepository newInstance(UserPreferences userPreferences) {
    return new CurrencyRepository(userPreferences);
  }
}
