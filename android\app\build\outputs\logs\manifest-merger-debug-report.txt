-- Merging decision tree log ---
manifest
ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:2:1-34:12
INJECTED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:2:1-34:12
INJECTED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:2:1-34:12
INJECTED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:2:1-34:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de56ff69481e934d0ac0a84174a747e5\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f011758bbd5e1f3717f8904866ac8535\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b92dab1abc180636dfa568108edbc72\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [co.yml:ycharts:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fac3eb0b059bea1a2f5c1953b353f8c9\transformed\ycharts-2.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18bb0f14d8d082a439e0519f9807b2b2\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fd5da91cefeec3228c5e48dd0aad8e1\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\079bacef6faeb9f9a455732eb9607686\transformed\navigation-common-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a8b0c355848d54770b025949c6a018f\transformed\navigation-runtime-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c334125fcab38c5c56f63820a7ba4d70\transformed\navigation-common-ktx-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\255035135215a8efb5a727052b20247a\transformed\navigation-runtime-ktx-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4076e5472473a5983c6f17a071e36df\transformed\navigation-compose-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba138936b038f506424ef0ea90c3bea9\transformed\hilt-android-2.52\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ea984abe4033c0c4682f2970fc520b\transformed\material3-window-size-class-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6ee2619a776087357431d65cec3fd3a\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab016abbdc1fb065aca79911845144f5\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2988ee760ed7d3727a9e73a2b75f783e\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ceff6e1f439620f00b03f890f9efcb6\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6171dafc34265bb2c28c09f51d738d25\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95bfa9e29ba6723aa7025091734e841e\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\668b07b585425ac50b3664ae01d10c25\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc0ac76b2a541c7470271afa4899af52\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8aa954ac2006ffefced330a26c712e54\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a432069da9de815a0629ea6eb78a0370\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\598ca44db4ef3f33d19e86836430f01b\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea7cf333c563b8f28616ae4a0822da68\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4a866ea164dc20139173fb7449a3064\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eace484f4aa5db8bdf18800217b1f015\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\683384900b47375005ae7f584e543a91\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e3c8b92233f242895d4710e348a84a0\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a5446c44aa521f2373650cce03072da\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5bb51c3d43641a7411f79d0d3d31b1e5\transformed\activity-1.9.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f591b2a0b12c0fba2a305931fba7f401\transformed\activity-ktx-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1056e65497222773743dc4f1e1ee12d8\transformed\activity-compose-1.9.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32d28b3e6fd798fa479222da51236e61\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59944556d3ccf5c33c2cc757963b127f\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\976acf7b58bfddeeaebee4e05dabb33f\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90cf2d437962f7f16b930e874986bf50\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\082e29645f6985bd13df25c125e8d09f\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2502f39570bde653919c3436da0a6de1\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a72a21877b683f821335951ac9c9599\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3887cc792a4ad23cc02050b37b01e71\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0312a6e9ced2c28e9a6c102442d7dd98\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2eabb666d63b156c4f2e2ef4076ea315\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\340ed8e699a9abf713dcc38bc6e0f121\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\841c4d61dcc6f41c843e8ee9f89cbea9\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4467306274511ad29b5464bbfabed49\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6a4325e9e2fccad19a2668fb75db8f6\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b459124a6ff47dc9060e80bdac56618\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbbbfa3c521df5f27850d14984370b67\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e5c272ed8753b7bca75d20a7afd60fc\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63e29ebdad588189bbd8c28491806348\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b90158aad7c8de2bb2b6ce50c566fed9\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67884fed1cf758142e07b81e0b249333\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\069528ffaa29dd500afb9b9cea2a872f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8096e1d6f1a98bc40affc8cf63497a3\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3791d9f5a047de7c5458deafe305b6\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4482b405f078909782e71a211d28aef9\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ecd25e09b4f2ddb52b3cf5bd922c6f7f\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9f7368b08ea7780a30cc4aa9ee47ccf\transformed\window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c8287efcc424c1657e6673c185d7f75\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c5897f300a13f7a0877fb2b6be20d30\transformed\core-ktx-1.15.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3037813a97b3cb97a9a746c0b34721d\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45248d7348c31341941f54a6ee20713a\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\912b4de503efbcb5180527c49fe7c1ea\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84c5044a704500b9400c19f6376ade2e\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f99cc9c5b61cbdbe11643d6dc0173b9\transformed\datastore-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4c65985edd073278b896b3b38c766d8\transformed\datastore-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5fb87ae3dfffdc9f822755559a6a86ea\transformed\datastore-preferences-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\412b1c600b48b36771567fd1699c9ded\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59ff98bca8c9a47167c58b9861190c35\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a42cb956d92086de895e35efa030d58c\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e66720b802ae0ceed44631764c9d9b5e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b21d2a882928763789acc285e329648\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68c2fd99a54958c140d758cbb249a719\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00bedff45accf9bd4d8f81b3522d99de\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47ce2012696d78bf3a6d334fd70c001\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20031b66ed4fb96d97e78016cc97c6cb\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276cdf95ae13c6535dd12b4a3e7a0cbd\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:16:1-19:12
	package
		INJECTED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:6:5-79
	android:name
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:6:22-76
application
ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:8:5-32:19
INJECTED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:8:5-32:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eace484f4aa5db8bdf18800217b1f015\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eace484f4aa5db8bdf18800217b1f015\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e3c8b92233f242895d4710e348a84a0\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e3c8b92233f242895d4710e348a84a0\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\340ed8e699a9abf713dcc38bc6e0f121\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\340ed8e699a9abf713dcc38bc6e0f121\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b90158aad7c8de2bb2b6ce50c566fed9\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b90158aad7c8de2bb2b6ce50c566fed9\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9f7368b08ea7780a30cc4aa9ee47ccf\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9f7368b08ea7780a30cc4aa9ee47ccf\transformed\window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3037813a97b3cb97a9a746c0b34721d\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3037813a97b3cb97a9a746c0b34721d\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e66720b802ae0ceed44631764c9d9b5e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e66720b802ae0ceed44631764c9d9b5e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68c2fd99a54958c140d758cbb249a719\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68c2fd99a54958c140d758cbb249a719\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml
	android:roundIcon
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:15:9-54
	android:icon
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:13:9-43
	android:networkSecurityConfig
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:19:9-69
	android:appComponentFactory
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:16:9-35
	android:label
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:14:9-41
	android:fullBackupContent
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:12:9-54
	tools:targetApi
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:20:9-29
	android:allowBackup
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:10:9-35
	android:theme
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:17:9-50
	android:dataExtractionRules
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:11:9-65
	android:usesCleartextTraffic
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:18:9-44
	android:name
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:9:9-48
activity#com.quickspender.android.MainActivity
ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:21:9-31:20
	android:label
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:24:13-45
	android:exported
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:23:13-36
	android:theme
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:25:13-54
	android:name
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:22:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:26:13-30:29
action#android.intent.action.MAIN
ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:27:17-69
	android:name
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:27:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:29:17-77
	android:name
		ADDED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:29:27-74
uses-sdk
INJECTED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml
INJECTED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de56ff69481e934d0ac0a84174a747e5\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\de56ff69481e934d0ac0a84174a747e5\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f011758bbd5e1f3717f8904866ac8535\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f011758bbd5e1f3717f8904866ac8535\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b92dab1abc180636dfa568108edbc72\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5b92dab1abc180636dfa568108edbc72\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [co.yml:ycharts:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fac3eb0b059bea1a2f5c1953b353f8c9\transformed\ycharts-2.1.0\AndroidManifest.xml:5:5-44
MERGED from [co.yml:ycharts:2.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\fac3eb0b059bea1a2f5c1953b353f8c9\transformed\ycharts-2.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18bb0f14d8d082a439e0519f9807b2b2\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\18bb0f14d8d082a439e0519f9807b2b2\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fd5da91cefeec3228c5e48dd0aad8e1\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3fd5da91cefeec3228c5e48dd0aad8e1\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\079bacef6faeb9f9a455732eb9607686\transformed\navigation-common-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\079bacef6faeb9f9a455732eb9607686\transformed\navigation-common-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a8b0c355848d54770b025949c6a018f\transformed\navigation-runtime-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a8b0c355848d54770b025949c6a018f\transformed\navigation-runtime-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c334125fcab38c5c56f63820a7ba4d70\transformed\navigation-common-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c334125fcab38c5c56f63820a7ba4d70\transformed\navigation-common-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\255035135215a8efb5a727052b20247a\transformed\navigation-runtime-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\255035135215a8efb5a727052b20247a\transformed\navigation-runtime-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4076e5472473a5983c6f17a071e36df\transformed\navigation-compose-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b4076e5472473a5983c6f17a071e36df\transformed\navigation-compose-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba138936b038f506424ef0ea90c3bea9\transformed\hilt-android-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ba138936b038f506424ef0ea90c3bea9\transformed\hilt-android-2.52\AndroidManifest.xml:18:3-42
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ea984abe4033c0c4682f2970fc520b\transformed\material3-window-size-class-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-window-size-class-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\72ea984abe4033c0c4682f2970fc520b\transformed\material3-window-size-class-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6ee2619a776087357431d65cec3fd3a\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6ee2619a776087357431d65cec3fd3a\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab016abbdc1fb065aca79911845144f5\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ab016abbdc1fb065aca79911845144f5\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2988ee760ed7d3727a9e73a2b75f783e\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2988ee760ed7d3727a9e73a2b75f783e\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ceff6e1f439620f00b03f890f9efcb6\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2ceff6e1f439620f00b03f890f9efcb6\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6171dafc34265bb2c28c09f51d738d25\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6171dafc34265bb2c28c09f51d738d25\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95bfa9e29ba6723aa7025091734e841e\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\95bfa9e29ba6723aa7025091734e841e\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\668b07b585425ac50b3664ae01d10c25\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\668b07b585425ac50b3664ae01d10c25\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc0ac76b2a541c7470271afa4899af52\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dc0ac76b2a541c7470271afa4899af52\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8aa954ac2006ffefced330a26c712e54\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8aa954ac2006ffefced330a26c712e54\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a432069da9de815a0629ea6eb78a0370\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a432069da9de815a0629ea6eb78a0370\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\598ca44db4ef3f33d19e86836430f01b\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\598ca44db4ef3f33d19e86836430f01b\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea7cf333c563b8f28616ae4a0822da68\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ea7cf333c563b8f28616ae4a0822da68\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4a866ea164dc20139173fb7449a3064\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f4a866ea164dc20139173fb7449a3064\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eace484f4aa5db8bdf18800217b1f015\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eace484f4aa5db8bdf18800217b1f015\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\683384900b47375005ae7f584e543a91\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\683384900b47375005ae7f584e543a91\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e3c8b92233f242895d4710e348a84a0\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e3c8b92233f242895d4710e348a84a0\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a5446c44aa521f2373650cce03072da\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a5446c44aa521f2373650cce03072da\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5bb51c3d43641a7411f79d0d3d31b1e5\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5bb51c3d43641a7411f79d0d3d31b1e5\transformed\activity-1.9.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f591b2a0b12c0fba2a305931fba7f401\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f591b2a0b12c0fba2a305931fba7f401\transformed\activity-ktx-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1056e65497222773743dc4f1e1ee12d8\transformed\activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.9.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1056e65497222773743dc4f1e1ee12d8\transformed\activity-compose-1.9.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32d28b3e6fd798fa479222da51236e61\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\32d28b3e6fd798fa479222da51236e61\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59944556d3ccf5c33c2cc757963b127f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59944556d3ccf5c33c2cc757963b127f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\976acf7b58bfddeeaebee4e05dabb33f\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\976acf7b58bfddeeaebee4e05dabb33f\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90cf2d437962f7f16b930e874986bf50\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\90cf2d437962f7f16b930e874986bf50\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\082e29645f6985bd13df25c125e8d09f\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\082e29645f6985bd13df25c125e8d09f\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2502f39570bde653919c3436da0a6de1\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2502f39570bde653919c3436da0a6de1\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a72a21877b683f821335951ac9c9599\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9a72a21877b683f821335951ac9c9599\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3887cc792a4ad23cc02050b37b01e71\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d3887cc792a4ad23cc02050b37b01e71\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0312a6e9ced2c28e9a6c102442d7dd98\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0312a6e9ced2c28e9a6c102442d7dd98\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2eabb666d63b156c4f2e2ef4076ea315\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2eabb666d63b156c4f2e2ef4076ea315\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\340ed8e699a9abf713dcc38bc6e0f121\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\340ed8e699a9abf713dcc38bc6e0f121\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\841c4d61dcc6f41c843e8ee9f89cbea9\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\841c4d61dcc6f41c843e8ee9f89cbea9\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4467306274511ad29b5464bbfabed49\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\d4467306274511ad29b5464bbfabed49\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6a4325e9e2fccad19a2668fb75db8f6\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e6a4325e9e2fccad19a2668fb75db8f6\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b459124a6ff47dc9060e80bdac56618\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4b459124a6ff47dc9060e80bdac56618\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbbbfa3c521df5f27850d14984370b67\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dbbbfa3c521df5f27850d14984370b67\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e5c272ed8753b7bca75d20a7afd60fc\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7e5c272ed8753b7bca75d20a7afd60fc\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63e29ebdad588189bbd8c28491806348\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\63e29ebdad588189bbd8c28491806348\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b90158aad7c8de2bb2b6ce50c566fed9\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b90158aad7c8de2bb2b6ce50c566fed9\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67884fed1cf758142e07b81e0b249333\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67884fed1cf758142e07b81e0b249333\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\069528ffaa29dd500afb9b9cea2a872f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\069528ffaa29dd500afb9b9cea2a872f\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8096e1d6f1a98bc40affc8cf63497a3\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c8096e1d6f1a98bc40affc8cf63497a3\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3791d9f5a047de7c5458deafe305b6\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6a3791d9f5a047de7c5458deafe305b6\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4482b405f078909782e71a211d28aef9\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4482b405f078909782e71a211d28aef9\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ecd25e09b4f2ddb52b3cf5bd922c6f7f\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ecd25e09b4f2ddb52b3cf5bd922c6f7f\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9f7368b08ea7780a30cc4aa9ee47ccf\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9f7368b08ea7780a30cc4aa9ee47ccf\transformed\window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c8287efcc424c1657e6673c185d7f75\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9c8287efcc424c1657e6673c185d7f75\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c5897f300a13f7a0877fb2b6be20d30\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5c5897f300a13f7a0877fb2b6be20d30\transformed\core-ktx-1.15.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3037813a97b3cb97a9a746c0b34721d\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3037813a97b3cb97a9a746c0b34721d\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45248d7348c31341941f54a6ee20713a\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\45248d7348c31341941f54a6ee20713a\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\912b4de503efbcb5180527c49fe7c1ea\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\912b4de503efbcb5180527c49fe7c1ea\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84c5044a704500b9400c19f6376ade2e\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\84c5044a704500b9400c19f6376ade2e\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f99cc9c5b61cbdbe11643d6dc0173b9\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\3f99cc9c5b61cbdbe11643d6dc0173b9\transformed\datastore-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4c65985edd073278b896b3b38c766d8\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e4c65985edd073278b896b3b38c766d8\transformed\datastore-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5fb87ae3dfffdc9f822755559a6a86ea\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5fb87ae3dfffdc9f822755559a6a86ea\transformed\datastore-preferences-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\412b1c600b48b36771567fd1699c9ded\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\412b1c600b48b36771567fd1699c9ded\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59ff98bca8c9a47167c58b9861190c35\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\59ff98bca8c9a47167c58b9861190c35\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a42cb956d92086de895e35efa030d58c\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a42cb956d92086de895e35efa030d58c\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e66720b802ae0ceed44631764c9d9b5e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e66720b802ae0ceed44631764c9d9b5e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b21d2a882928763789acc285e329648\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7b21d2a882928763789acc285e329648\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68c2fd99a54958c140d758cbb249a719\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\68c2fd99a54958c140d758cbb249a719\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00bedff45accf9bd4d8f81b3522d99de\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\00bedff45accf9bd4d8f81b3522d99de\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47ce2012696d78bf3a6d334fd70c001\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b47ce2012696d78bf3a6d334fd70c001\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20031b66ed4fb96d97e78016cc97c6cb\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\20031b66ed4fb96d97e78016cc97c6cb\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276cdf95ae13c6535dd12b4a3e7a0cbd\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\276cdf95ae13c6535dd12b4a3e7a0cbd\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:18:3-42
	android:targetSdkVersion
		INJECTED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eace484f4aa5db8bdf18800217b1f015\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eace484f4aa5db8bdf18800217b1f015\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eace484f4aa5db8bdf18800217b1f015\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e3c8b92233f242895d4710e348a84a0\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e3c8b92233f242895d4710e348a84a0\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e3c8b92233f242895d4710e348a84a0\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\340ed8e699a9abf713dcc38bc6e0f121\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b90158aad7c8de2bb2b6ce50c566fed9\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b90158aad7c8de2bb2b6ce50c566fed9\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e66720b802ae0ceed44631764c9d9b5e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e66720b802ae0ceed44631764c9d9b5e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\340ed8e699a9abf713dcc38bc6e0f121\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\340ed8e699a9abf713dcc38bc6e0f121\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\340ed8e699a9abf713dcc38bc6e0f121\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\340ed8e699a9abf713dcc38bc6e0f121\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\340ed8e699a9abf713dcc38bc6e0f121\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\340ed8e699a9abf713dcc38bc6e0f121\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\340ed8e699a9abf713dcc38bc6e0f121\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b90158aad7c8de2bb2b6ce50c566fed9\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b90158aad7c8de2bb2b6ce50c566fed9\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b90158aad7c8de2bb2b6ce50c566fed9\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9f7368b08ea7780a30cc4aa9ee47ccf\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9f7368b08ea7780a30cc4aa9ee47ccf\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9f7368b08ea7780a30cc4aa9ee47ccf\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9f7368b08ea7780a30cc4aa9ee47ccf\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9f7368b08ea7780a30cc4aa9ee47ccf\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9f7368b08ea7780a30cc4aa9ee47ccf\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
permission#com.quickspender.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
uses-permission#com.quickspender.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3037813a97b3cb97a9a746c0b34721d\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3037813a97b3cb97a9a746c0b34721d\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3037813a97b3cb97a9a746c0b34721d\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3037813a97b3cb97a9a746c0b34721d\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3037813a97b3cb97a9a746c0b34721d\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
