# Use Ubuntu base image since we're using self-contained deployment
FROM ubuntu:22.04 AS runtime

# Install required dependencies for self-contained .NET app
RUN apt-get update && apt-get install -y \
    curl \
    ca-certificates \
    libicu70 \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create a non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser -u 1000 appuser

# Set the working directory
WORKDIR /app

# Copy the pre-built self-contained application
COPY app/ .

# Create data directory for application files
RUN mkdir -p /app/data && \
    chown -R appuser:appuser /app && \
    chmod +x /app/QuickSpender.API

# Switch to non-root user
USER appuser

# Expose the port the app runs on
EXPOSE 8080

# Set environment variables
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:8080
ENV DOTNET_RUNNING_IN_CONTAINER=true
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=1

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Start the self-contained application
ENTRYPOINT ["./QuickSpender.API"]
