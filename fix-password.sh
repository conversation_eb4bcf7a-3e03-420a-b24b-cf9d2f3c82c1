#!/bin/bash

# Quick fix for password authentication issue

echo "🔧 Fixing QuickSpender password authentication..."

# Configuration
REMOTE_HOST="${REMOTE_HOST:-***************}"
REMOTE_USER="${REMOTE_USER:-<PERSON><PERSON><PERSON><PERSON>}"
SSH_KEY="${SSH_KEY:-C:/Users/<USER>/.ssh/id_rsa}"

# Prompt for sudo password if not set
if [ -z "$SUDO_PASSWORD" ]; then
    echo "🔐 Enter sudo password for $REMOTE_USER@$REMOTE_HOST:"
    read -s SUDO_PASSWORD
    export SUDO_PASSWORD
fi

echo "🔗 Connecting to server to fix password issue..."

ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" << EOF
    echo "🗄️  Fixing database user password..."
    
    # Update database user password
    echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -c "ALTER USER quickspender WITH PASSWORD 'd9th0I1ajnXr2N2inE94zyzC796SceJcukq';"
    echo "✅ Database user password updated"
    
    # Fix .env file
    cd /home/<USER>/quickspender
    echo "🔧 Updating .env file..."
    sed -i 's/CONNECTION_STRING=.*/CONNECTION_STRING=Host=host.docker.internal;Port=5432;Database=quickspender;Username=quickspender;Password=************************************' .env
    sed -i 's/DB_PASSWORD=.*/DB_PASSWORD=************************************' .env
    sed -i 's/DB_HOST=.*/DB_HOST=host.docker.internal/' .env
    echo "✅ .env file updated"
    
    # Test database connection
    echo "🧪 Testing database connection..."
    export PGPASSWORD='d9th0I1ajnXr2N2inE94zyzC796SceJcukq'
    if psql -h localhost -p 5432 -U quickspender -d quickspender -c "SELECT 1;" &>/dev/null; then
        echo "✅ Database connection successful"
    else
        echo "❌ Database connection still failing"
    fi
    unset PGPASSWORD
    
    # Restart container
    echo "🔄 Restarting container with fixed configuration..."
    echo "$SUDO_PASSWORD" | sudo -S docker-compose down
    echo "$SUDO_PASSWORD" | sudo -S docker-compose up -d
    
    echo "⏳ Waiting for container to start..."
    sleep 15
    
    echo "📊 Container status:"
    echo "$SUDO_PASSWORD" | sudo -S docker-compose ps
    
    echo "📋 Recent container logs:"
    echo "$SUDO_PASSWORD" | sudo -S docker-compose logs --tail=10
    
    # Test API
    echo "🧪 Testing API..."
    sleep 5
    if curl -f http://localhost:8080/health 2>/dev/null; then
        echo "✅ API is working!"
    else
        echo "❌ API still not responding"
    fi
EOF

echo ""
echo "✅ Password fix completed!"
echo ""
echo "🌐 If successful, your API should be available at:"
echo "   • http://***************/quickspender/"
echo "   • http://***************/quickspender/health"
