using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using QuickSpender.Application.Common.Interfaces;
using QuickSpender.Contracts.Categories;
using QuickSpender.Domain.Enums;

namespace QuickSpender.Application.Categories.Queries.GetCategories;

public class GetCategoriesQueryHandler : IRequestHandler<GetCategoriesQuery, IEnumerable<CategoryResponse>>
{
    private readonly IApplicationDbContext _context;
    private readonly ICurrentUserService _currentUserService;
    private readonly IMapper _mapper;

    public GetCategoriesQueryHandler(
        IApplicationDbContext context,
        ICurrentUserService currentUserService,
        IMapper mapper)
    {
        _context = context;
        _currentUserService = currentUserService;
        _mapper = mapper;
    }

    public async Task<IEnumerable<CategoryResponse>> Handle(GetCategoriesQuery request, CancellationToken cancellationToken)
    {
        if (!_currentUserService.IsAuthenticated || _currentUserService.UserId is null)
        {
            throw new UnauthorizedAccessException("User is not authenticated.");
        }

        // Only return categories that belong to the current user
        // Global default categories should not be returned since each user gets their own copy during registration
        var query = _context.Categories
            .Where(c => c.UserId == _currentUserService.UserId);

        if (request.Type.HasValue)
        {
            query = query.Where(c => c.Type == (CategoryType)request.Type.Value);
        }

        var categories = await query
            .OrderBy(c => c.Name)
            .ToListAsync(cancellationToken);

        Console.WriteLine($"GetCategories: Found {categories.Count} categories for user {_currentUserService.UserId}");
        foreach (var cat in categories)
        {
            Console.WriteLine($"GetCategories: Category - ID: {cat.Id}, Name: {cat.Name}, UserId: {cat.UserId}, IsDefault: {cat.IsDefault}");
        }

        return _mapper.Map<IEnumerable<CategoryResponse>>(categories);
    }
}
