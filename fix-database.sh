#!/bin/bash

# Fix database connection issues for QuickSpender

echo "🔧 Fixing QuickSpender database issues..."

# Configuration
REMOTE_HOST="${REMOTE_HOST:-***************}"
REMOTE_USER="${REMOTE_USER:-ad<PERSON><PERSON><PERSON>}"
SSH_KEY="${SSH_KEY:-C:/Users/<USER>/.ssh/id_rsa}"

# Prompt for sudo password if not set
if [ -z "$SUDO_PASSWORD" ]; then
    echo "🔐 Enter sudo password for $REMOTE_USER@$REMOTE_HOST:"
    read -s SUDO_PASSWORD
    export SUDO_PASSWORD
fi

echo "🔗 Connecting to server to fix database..."

ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" << EOF
    echo "🔍 Checking PostgreSQL status..."
    
    # Check if PostgreSQL is running
    if ! systemctl is-active --quiet postgresql; then
        echo "🚀 Starting PostgreSQL service..."
        echo "$SUDO_PASSWORD" | sudo -S systemctl start postgresql
        echo "$SUDO_PASSWORD" | sudo -S systemctl enable postgresql
    else
        echo "✅ PostgreSQL service is running"
    fi
    
    # Check if database exists
    echo "🗄️  Checking database setup..."
    if echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -lqt | cut -d \\| -f 1 | grep -qw quickspender; then
        echo "✅ Database 'quickspender' exists"
    else
        echo "❌ Database 'quickspender' does not exist. Creating..."
        echo "$SUDO_PASSWORD" | sudo -S -u postgres createdb quickspender
    fi
    
    # Check if user exists
    if echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -tAc "SELECT 1 FROM pg_roles WHERE rolname='quickspender'" | grep -q 1; then
        echo "✅ User 'quickspender' exists"
    else
        echo "❌ User 'quickspender' does not exist. Creating..."
        echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -c "CREATE USER quickspender WITH PASSWORD '***********************************';"
    fi
    
    # Grant permissions
    echo "🔑 Setting up database permissions..."
    echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE quickspender TO quickspender;"
    echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -c "ALTER USER quickspender CREATEDB;"
    
    # Test connection
    echo "🧪 Testing database connection..."
    if echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -c "SELECT 1;" quickspender &>/dev/null; then
        echo "✅ Database connection successful"
    else
        echo "❌ Database connection failed"
    fi
    
    # Check PostgreSQL configuration
    echo "⚙️  Checking PostgreSQL configuration..."
    PG_VERSION=\$(echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -tAc "SELECT version();" | grep -oP 'PostgreSQL \\K[0-9]+')
    echo "PostgreSQL version: \$PG_VERSION"
    
    # Ensure PostgreSQL accepts local connections
    PG_HBA_FILE="/etc/postgresql/\$PG_VERSION/main/pg_hba.conf"
    if [ -f "\$PG_HBA_FILE" ]; then
        echo "📝 Checking pg_hba.conf..."
        if grep -q "local.*quickspender.*quickspender.*md5" "\$PG_HBA_FILE"; then
            echo "✅ Database authentication configured"
        else
            echo "⚠️  Adding database authentication rule..."
            echo "$SUDO_PASSWORD" | sudo -S sh -c "echo 'local   quickspender    quickspender                    md5' >> \$PG_HBA_FILE"
            echo "$SUDO_PASSWORD" | sudo -S systemctl reload postgresql
        fi
    fi
    
    # Show current database info
    echo ""
    echo "📊 Database Information:"
    echo "Database: quickspender"
    echo "User: quickspender"
    echo "Host: localhost"
    echo "Port: 5432"
    echo ""
    
    # Fix .env file if needed
    cd /home/<USER>/quickspender
    if [ -f .env ]; then
        echo "🔧 Checking .env file..."
        if grep -q "Host=host.docker.internal;Port=5432;Database=quickspender;Username=quickspender;Password=***********************************" .env; then
            echo "✅ .env file has correct connection string for Docker"
        else
            echo "⚠️  Fixing connection string in .env file for Docker container..."
            sed -i 's/CONNECTION_STRING=.*/CONNECTION_STRING=Host=host.docker.internal;Port=5432;Database=quickspender;Username=quickspender;Password=***********************************/' .env
            echo "✅ Connection string updated for Docker container"
        fi
    fi

    # Configure PostgreSQL to accept connections from Docker containers
    echo "🔧 Configuring PostgreSQL for Docker container access..."
    PG_VERSION=\$(echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -tAc "SELECT version();" | grep -oP 'PostgreSQL \\K[0-9]+')
    PG_CONF_FILE="/etc/postgresql/\$PG_VERSION/main/postgresql.conf"

    if [ -f "\$PG_CONF_FILE" ]; then
        # Enable listening on all addresses
        if ! grep -q "listen_addresses = '\\*'" "\$PG_CONF_FILE"; then
            echo "$SUDO_PASSWORD" | sudo -S sed -i "s/#listen_addresses = 'localhost'/listen_addresses = '*'/" "\$PG_CONF_FILE"
            echo "✅ PostgreSQL configured to listen on all addresses"
            RESTART_PG=true
        fi
    fi

    # Update pg_hba.conf for Docker container access
    PG_HBA_FILE="/etc/postgresql/\$PG_VERSION/main/pg_hba.conf"
    if [ -f "\$PG_HBA_FILE" ]; then
        # Add rule for Docker containers
        if ! grep -q "host.*quickspender.*quickspender.**********/8.*md5" "\$PG_HBA_FILE"; then
            echo "$SUDO_PASSWORD" | sudo -S sh -c "echo 'host    quickspender    quickspender    *********/8            md5' >> \$PG_HBA_FILE"
            echo "✅ Added Docker container access rule to pg_hba.conf"
            RESTART_PG=true
        fi
    fi

    # Restart PostgreSQL if configuration changed
    if [ "\$RESTART_PG" = "true" ]; then
        echo "🔄 Restarting PostgreSQL to apply configuration changes..."
        echo "$SUDO_PASSWORD" | sudo -S systemctl restart postgresql
        sleep 5
    fi
EOF

echo ""
echo "✅ Database fix completed!"
echo ""
echo "💡 Next steps:"
echo "   1. Restart the container: ssh to server and run 'sudo docker-compose restart'"
echo "   2. Or redeploy: ./deploy.sh"
echo "   3. Check container logs: ssh to server and run 'sudo docker-compose logs'"
