#!/bin/bash

# Fix database connection issues for QuickSpender

echo "🔧 Fixing QuickSpender database issues..."

# Configuration
REMOTE_HOST="${REMOTE_HOST:-***************}"
REMOTE_USER="${REMOTE_USER:-ad<PERSON><PERSON><PERSON>}"
SSH_KEY="${SSH_KEY:-C:/Users/<USER>/.ssh/id_rsa}"

# Prompt for sudo password if not set
if [ -z "$SUDO_PASSWORD" ]; then
    echo "🔐 Enter sudo password for $REMOTE_USER@$REMOTE_HOST:"
    read -s SUDO_PASSWORD
    export SUDO_PASSWORD
fi

echo "🔗 Connecting to server to fix database..."

ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" << EOF
    echo "🔍 Checking PostgreSQL status..."
    
    # Check if PostgreSQL is running
    if ! systemctl is-active --quiet postgresql; then
        echo "🚀 Starting PostgreSQL service..."
        echo "$SUDO_PASSWORD" | sudo -S systemctl start postgresql
        echo "$SUDO_PASSWORD" | sudo -S systemctl enable postgresql
    else
        echo "✅ PostgreSQL service is running"
    fi
    
    # Check if database exists
    echo "🗄️  Checking database setup..."
    if echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -lqt | cut -d \\| -f 1 | grep -qw quickspender; then
        echo "✅ Database 'quickspender' exists"
    else
        echo "❌ Database 'quickspender' does not exist. Creating..."
        echo "$SUDO_PASSWORD" | sudo -S -u postgres createdb quickspender
    fi
    
    # Check if user exists
    if echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -tAc "SELECT 1 FROM pg_roles WHERE rolname='quickspender'" | grep -q 1; then
        echo "✅ User 'quickspender' exists"
    else
        echo "❌ User 'quickspender' does not exist. Creating..."
        echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -c "CREATE USER quickspender WITH PASSWORD '***********************************';"
    fi
    
    # Grant permissions
    echo "🔑 Setting up database permissions..."
    echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE quickspender TO quickspender;"
    echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -c "ALTER USER quickspender CREATEDB;"
    
    # Test connection
    echo "🧪 Testing database connection..."
    if echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -c "SELECT 1;" quickspender &>/dev/null; then
        echo "✅ Database connection successful"
    else
        echo "❌ Database connection failed"
    fi
    
    # Check PostgreSQL configuration
    echo "⚙️  Checking PostgreSQL configuration..."
    PG_VERSION=\$(echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -tAc "SELECT version();" | grep -oP 'PostgreSQL \\K[0-9]+')
    echo "PostgreSQL version: \$PG_VERSION"
    
    # Ensure PostgreSQL accepts local connections
    PG_HBA_FILE="/etc/postgresql/\$PG_VERSION/main/pg_hba.conf"
    if [ -f "\$PG_HBA_FILE" ]; then
        echo "📝 Checking pg_hba.conf..."
        if grep -q "local.*quickspender.*quickspender.*md5" "\$PG_HBA_FILE"; then
            echo "✅ Database authentication configured"
        else
            echo "⚠️  Adding database authentication rule..."
            echo "$SUDO_PASSWORD" | sudo -S sh -c "echo 'local   quickspender    quickspender                    md5' >> \$PG_HBA_FILE"
            echo "$SUDO_PASSWORD" | sudo -S systemctl reload postgresql
        fi
    fi
    
    # Show current database info
    echo ""
    echo "📊 Database Information:"
    echo "Database: quickspender"
    echo "User: quickspender"
    echo "Host: localhost"
    echo "Port: 5432"
    echo ""
    
    # Fix .env file if needed
    cd /home/<USER>/quickspender
    if [ -f .env ]; then
        echo "🔧 Checking .env file..."
        if grep -q "Host=localhost;Port=5432;Database=quickspender;Username=quickspender;Password=***********************************" .env; then
            echo "✅ .env file has correct connection string"
        else
            echo "⚠️  Fixing connection string in .env file..."
            sed -i 's/CONNECTION_STRING=.*/CONNECTION_STRING=Host=localhost;Port=5432;Database=quickspender;Username=quickspender;Password=************************************' .env
            echo "✅ Connection string updated"
        fi
    fi
EOF

echo ""
echo "✅ Database fix completed!"
echo ""
echo "💡 Next steps:"
echo "   1. Restart the container: ssh to server and run 'sudo docker-compose restart'"
echo "   2. Or redeploy: ./deploy.sh"
echo "   3. Check container logs: ssh to server and run 'sudo docker-compose logs'"
