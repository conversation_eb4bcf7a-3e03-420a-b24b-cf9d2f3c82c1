package com.quickspender.android.ui.screens.dashboard;

import com.quickspender.android.data.repository.CurrencyRepository;
import com.quickspender.android.data.repository.ReportRepository;
import com.quickspender.android.data.repository.TransactionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DashboardViewModel_Factory implements Factory<DashboardViewModel> {
  private final Provider<TransactionRepository> transactionRepositoryProvider;

  private final Provider<ReportRepository> reportRepositoryProvider;

  private final Provider<CurrencyRepository> currencyRepositoryProvider;

  public DashboardViewModel_Factory(Provider<TransactionRepository> transactionRepositoryProvider,
      Provider<ReportRepository> reportRepositoryProvider,
      Provider<CurrencyRepository> currencyRepositoryProvider) {
    this.transactionRepositoryProvider = transactionRepositoryProvider;
    this.reportRepositoryProvider = reportRepositoryProvider;
    this.currencyRepositoryProvider = currencyRepositoryProvider;
  }

  @Override
  public DashboardViewModel get() {
    return newInstance(transactionRepositoryProvider.get(), reportRepositoryProvider.get(), currencyRepositoryProvider.get());
  }

  public static DashboardViewModel_Factory create(
      Provider<TransactionRepository> transactionRepositoryProvider,
      Provider<ReportRepository> reportRepositoryProvider,
      Provider<CurrencyRepository> currencyRepositoryProvider) {
    return new DashboardViewModel_Factory(transactionRepositoryProvider, reportRepositoryProvider, currencyRepositoryProvider);
  }

  public static DashboardViewModel newInstance(TransactionRepository transactionRepository,
      ReportRepository reportRepository, CurrencyRepository currencyRepository) {
    return new DashboardViewModel(transactionRepository, reportRepository, currencyRepository);
  }
}
