package com.quickspender.android.ui.screens.dashboard;

import com.quickspender.android.data.repository.ReportRepository;
import com.quickspender.android.data.repository.TransactionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DashboardViewModel_Factory implements Factory<DashboardViewModel> {
  private final Provider<TransactionRepository> transactionRepositoryProvider;

  private final Provider<ReportRepository> reportRepositoryProvider;

  public DashboardViewModel_Factory(Provider<TransactionRepository> transactionRepositoryProvider,
      Provider<ReportRepository> reportRepositoryProvider) {
    this.transactionRepositoryProvider = transactionRepositoryProvider;
    this.reportRepositoryProvider = reportRepositoryProvider;
  }

  @Override
  public DashboardViewModel get() {
    return newInstance(transactionRepositoryProvider.get(), reportRepositoryProvider.get());
  }

  public static DashboardViewModel_Factory create(
      Provider<TransactionRepository> transactionRepositoryProvider,
      Provider<ReportRepository> reportRepositoryProvider) {
    return new DashboardViewModel_Factory(transactionRepositoryProvider, reportRepositoryProvider);
  }

  public static DashboardViewModel newInstance(TransactionRepository transactionRepository,
      ReportRepository reportRepository) {
    return new DashboardViewModel(transactionRepository, reportRepository);
  }
}
