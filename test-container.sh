#!/bin/bash

# Test script to debug container startup issues

echo "🔍 Testing QuickSpender container startup..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running or not accessible"
    exit 1
fi

echo "✅ Docker is running"

# Check if the application binary exists and is executable
if [ -f "./app/QuickSpender.API" ]; then
    echo "✅ Application binary found"
    ls -la ./app/QuickSpender.API
    
    # Test if it's executable
    if [ -x "./app/QuickSpender.API" ]; then
        echo "✅ Application binary is executable"
    else
        echo "❌ Application binary is not executable"
        chmod +x ./app/QuickSpender.API
        echo "✅ Made application binary executable"
    fi
else
    echo "❌ Application binary not found at ./app/QuickSpender.API"
    echo "Available files in ./app/:"
    ls -la ./app/ 2>/dev/null || echo "No ./app/ directory found"
    exit 1
fi

# Test building the Docker image
echo "🔨 Building Docker image..."
if docker build -t quickspender-test .; then
    echo "✅ Docker image built successfully"
else
    echo "❌ Docker image build failed"
    exit 1
fi

# Test running the container
echo "🚀 Testing container startup..."
CONTAINER_ID=$(docker run -d -p 8081:8080 \
    -e ASPNETCORE_ENVIRONMENT=Development \
    -e ConnectionStrings__DefaultConnection="Host=host.docker.internal;Database=quickspender;Username=quickspender;Password=****************" \
    -e JwtSettings__Secret="your-super-secret-jwt-key-that-is-at-least-32-characters-long" \
    --name quickspender-test \
    quickspender-test)

if [ $? -eq 0 ]; then
    echo "✅ Container started with ID: $CONTAINER_ID"
    
    # Wait a moment for startup
    echo "⏳ Waiting for application to start..."
    sleep 10
    
    # Check container status
    echo "📊 Container status:"
    docker ps --filter "name=quickspender-test"
    
    # Check container logs
    echo "📋 Container logs:"
    docker logs quickspender-test
    
    # Test health endpoint
    echo "🏥 Testing health endpoint..."
    if curl -f http://localhost:8081/health 2>/dev/null; then
        echo "✅ Health endpoint is working!"
    else
        echo "❌ Health endpoint failed"
        echo "Trying to get more logs..."
        docker logs --tail=20 quickspender-test
    fi
    
    # Cleanup
    echo "🧹 Cleaning up test container..."
    docker stop quickspender-test
    docker rm quickspender-test
    docker rmi quickspender-test
    
else
    echo "❌ Container failed to start"
    exit 1
fi

echo "✅ Container test completed!"
