version: '3.8'

services:
  # QuickSpender API
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: quickspender-api
    environment:
      - ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT:-Production}
      - ASPNETCORE_URLS=http://+:8080
      - ConnectionStrings__DefaultConnection=${CONNECTION_STRING}
      - JwtSettings__Secret=${JWT_SECRET}
      - JwtSettings__Issuer=${JWT_ISSUER:-QuickSpender}
      - JwtSettings__Audience=${JWT_AUDIENCE:-QuickSpender}
      - JwtSettings__ExpiryMinutes=${JWT_EXPIRY_MINUTES:-60}
    ports:
      - "${API_PORT:-8080}:8080"
    restart: unless-stopped
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
    # Storage limits using tmpfs for temporary files
    tmpfs:
      - /tmp:size=100M,noexec,nosuid,nodev
    # Disk usage limit via volume
    volumes:
      - app_data:/app/data:rw
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - quickspender-network
    # Add extra hosts for database connection
    extra_hosts:
      - "host.docker.internal:host-gateway"
    # Security options
    security_opt:
      - no-new-privileges:true
    # Run as non-root user
    user: "1000:1000"

volumes:
  app_data:
    driver: local
    driver_opts:
      type: none
      o: bind,size=1G
      device: /var/lib/docker/volumes/quickspender_app_data

networks:
  quickspender-network:
    driver: bridge
