package com.quickspender.android.ui.screens.transactions

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.quickspender.android.data.model.Category
import com.quickspender.android.data.model.Currency
import com.quickspender.android.data.repository.CategoryRepository
import com.quickspender.android.data.repository.CurrencyRepository
import com.quickspender.android.data.repository.TransactionRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import javax.inject.Inject

@HiltViewModel
class AddTransactionViewModel @Inject constructor(
    private val transactionRepository: TransactionRepository,
    private val categoryRepository: CategoryRepository,
    private val currencyRepository: CurrencyRepository
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(AddTransactionUiState())
    val uiState: StateFlow<AddTransactionUiState> = _uiState.asStateFlow()
    
    fun loadCategories(type: Int) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)

                val currentCurrency = currencyRepository.getCurrentCurrencySync()
                val categories = categoryRepository.getCategories(type)

                _uiState.value = _uiState.value.copy(
                    categories = categories,
                    currentCurrency = currentCurrency,
                    isLoading = false
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "Failed to load categories: ${e.message}"
                )
            }
        }
    }
    
    fun addTransaction(
        amount: Double,
        description: String,
        notes: String?,
        type: Int,
        categoryId: String
    ) {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            val currentDateTime = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME)
            
            transactionRepository.createTransaction(
                amount = amount,
                description = description,
                notes = notes,
                type = type,
                categoryId = categoryId,
                transactionDate = currentDateTime
            ).onSuccess {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    transactionAdded = true
                )
            }.onFailure { error ->
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = error.message ?: "Failed to add transaction"
                )
            }
        }
    }

    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }

    fun resetTransactionAdded() {
        _uiState.value = _uiState.value.copy(transactionAdded = false)
    }
}

data class AddTransactionUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val categories: List<Category> = emptyList(),
    val transactionAdded: Boolean = false,
    val currentCurrency: Currency = Currency.USD
)
