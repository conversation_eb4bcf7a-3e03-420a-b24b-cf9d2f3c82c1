package com.quickspender.android.ui.components.charts

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import co.yml.charts.axis.AxisData
import co.yml.charts.common.model.Point
import co.yml.charts.ui.barchart.BarChart
import co.yml.charts.ui.barchart.models.BarChartData
import co.yml.charts.ui.barchart.models.BarData
import co.yml.charts.ui.barchart.models.BarStyle
import com.quickspender.android.data.model.CategorySummaryResponse

@Composable
fun CategoryBarChart(
    title: String,
    categoryData: List<CategorySummaryResponse>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            if (categoryData.isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "No data available",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            } else {
                // Take top 5 categories for better readability
                val topCategories = categoryData.take(5)
                
                // Prepare bar chart data
                val barData = topCategories.mapIndexed { index, category ->
                    BarData(
                        point = Point(index.toFloat(), category.totalAmount.toFloat()),
                        color = getColorForIndex(index),
                        label = category.category.name.take(8) // Truncate long names
                    )
                }
                
                val xAxisData = AxisData.Builder()
                    .axisStepSize(100.dp)
                    .backgroundColor(Color.Transparent)
                    .steps(topCategories.size - 1)
                    .labelData { i -> 
                        if (i < topCategories.size) {
                            topCategories[i].category.name.take(8)
                        } else ""
                    }
                    .labelAndAxisLinePadding(15.dp)
                    .build()

                val yAxisData = AxisData.Builder()
                    .steps(5)
                    .backgroundColor(Color.Transparent)
                    .labelAndAxisLinePadding(20.dp)
                    .labelData { i ->
                        val maxValue = topCategories.maxOfOrNull { it.totalAmount } ?: 0.0
                        val step = maxValue / 5
                        "$${String.format("%.0f", i * step)}"
                    }
                    .build()

                val barChartData = BarChartData(
                    chartData = barData,
                    xAxisData = xAxisData,
                    yAxisData = yAxisData,
                    barStyle = BarStyle(
                        paddingBetweenBars = 20.dp,
                        barWidth = 25.dp
                    ),
                    backgroundColor = Color.Transparent
                )
                
                BarChart(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(300.dp),
                    barChartData = barChartData
                )
                
                // Show summary below chart
                if (categoryData.size > 5) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "Showing top 5 of ${categoryData.size} categories",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            }
        }
    }
}

private fun getColorForIndex(index: Int): Color {
    val colors = listOf(
        Color(0xFF6366F1), // Indigo
        Color(0xFF8B5CF6), // Violet
        Color(0xFF06B6D4), // Cyan
        Color(0xFF10B981), // Emerald
        Color(0xFFF59E0B)  // Amber
    )
    return colors[index % colors.size]
}
