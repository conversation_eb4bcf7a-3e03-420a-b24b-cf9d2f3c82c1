package com.quickspender.android.utils

import com.quickspender.android.data.model.Currency
import java.text.DecimalFormat
import java.text.NumberFormat
import java.util.Locale

object CurrencyFormatter {
    
    fun formatAmount(amount: Double, currency: Currency): String {
        return when (currency) {
            Currency.USD, Currency.CAD, Currency.AUD, Currency.SGD, Currency.HKD -> {
                "${currency.symbol}${formatNumber(amount)}"
            }
            Currency.EUR -> {
                "${formatNumber(amount)} ${currency.symbol}"
            }
            Currency.PLN -> {
                "${formatNumber(amount)} ${currency.symbol}"
            }
            Currency.GBP -> {
                "${currency.symbol}${formatNumber(amount)}"
            }
            Currency.JPY, Currency.CNY, Currency.KRW -> {
                "${currency.symbol}${formatWholeNumber(amount)}"
            }
            Currency.CHF -> {
                "${currency.symbol} ${formatNumber(amount)}"
            }
            Currency.SEK, Currency.NOK, Currency.DKK -> {
                "${formatNumber(amount)} ${currency.symbol}"
            }
            Currency.CZK -> {
                "${formatNumber(amount)} ${currency.symbol}"
            }
            Currency.HUF -> {
                "${formatWholeNumber(amount)} ${currency.symbol}"
            }
            Currency.RUB -> {
                "${formatNumber(amount)} ${currency.symbol}"
            }
            Currency.BRL -> {
                "${currency.symbol} ${formatNumber(amount)}"
            }
            Currency.INR -> {
                "${currency.symbol} ${formatNumber(amount)}"
            }
        }
    }
    
    private fun formatNumber(amount: Double): String {
        val formatter = DecimalFormat("#,##0.00")
        return formatter.format(amount)
    }
    
    private fun formatWholeNumber(amount: Double): String {
        val formatter = DecimalFormat("#,##0")
        return formatter.format(amount)
    }
    
    fun getSymbol(currency: Currency): String {
        return currency.symbol
    }
    
    fun getCurrencyDisplayName(currency: Currency): String {
        return "${currency.symbol} ${currency.code}"
    }
}
