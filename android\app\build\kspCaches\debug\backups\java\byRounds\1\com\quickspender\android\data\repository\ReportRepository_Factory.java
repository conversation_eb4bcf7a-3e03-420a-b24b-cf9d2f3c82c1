package com.quickspender.android.data.repository;

import com.quickspender.android.data.api.ReportApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class ReportRepository_Factory implements Factory<ReportRepository> {
  private final Provider<ReportApi> reportApiProvider;

  public ReportRepository_Factory(Provider<ReportApi> reportApiProvider) {
    this.reportApiProvider = reportApiProvider;
  }

  @Override
  public ReportRepository get() {
    return newInstance(reportApiProvider.get());
  }

  public static ReportRepository_Factory create(Provider<ReportApi> reportApiProvider) {
    return new ReportRepository_Factory(reportApiProvider);
  }

  public static ReportRepository newInstance(ReportApi reportApi) {
    return new ReportRepository(reportApi);
  }
}
