package com.quickspender.android.ui.components.charts

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import co.yml.charts.common.model.PlotType
import co.yml.charts.ui.piechart.charts.PieChart
import co.yml.charts.ui.piechart.models.PieChartConfig
import co.yml.charts.ui.piechart.models.PieChartData
import com.quickspender.android.data.model.CategorySummaryResponse
import kotlin.math.roundToInt

@Composable
fun CategoryPieChart(
    title: String,
    categoryData: List<CategorySummaryResponse>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            
            if (categoryData.isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "No data available",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            } else {
                // Prepare pie chart data
                val pieChartData = categoryData.mapIndexed { index, category ->
                    PieChartData.Slice(
                        label = category.category.name,
                        value = category.totalAmount.toFloat(),
                        color = getColorForIndex(index)
                    )
                }
                
                val pieChartConfig = PieChartConfig(
                    labelVisible = true,
                    strokeWidth = 3f,
                    labelColor = MaterialTheme.colorScheme.onSurface,
                    activeSliceAlpha = .9f,
                    isEllipsizeEnabled = true
                )
                
                // Pie Chart
                PieChart(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(250.dp),
                    pieChartData = PieChartData(
                        slices = pieChartData,
                        plotType = PlotType.Pie
                    ),
                    pieChartConfig = pieChartConfig
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                // Legend
                LazyColumn(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(categoryData.take(5)) { category ->
                        CategoryLegendItem(
                            category = category,
                            color = getColorForIndex(categoryData.indexOf(category))
                        )
                    }
                    
                    if (categoryData.size > 5) {
                        item {
                            Text(
                                text = "... and ${categoryData.size - 5} more",
                                style = MaterialTheme.typography.bodySmall,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.padding(start = 32.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun CategoryLegendItem(
    category: CategorySummaryResponse,
    color: Color,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Color indicator
            Box(
                modifier = Modifier
                    .size(16.dp)
                    .padding(end = 8.dp)
            ) {
                Card(
                    modifier = Modifier.fillMaxSize(),
                    colors = CardDefaults.cardColors(containerColor = color)
                ) {}
            }
            
            Column {
                Text(
                    text = category.category.name,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "${category.transactionCount} transactions",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
        
        Column(
            horizontalAlignment = Alignment.End
        ) {
            Text(
                text = "$${String.format("%.2f", category.totalAmount)}",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = "${category.percentage.roundToInt()}%",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.primary
            )
        }
    }
}

private fun getColorForIndex(index: Int): Color {
    val colors = listOf(
        Color(0xFF6366F1), // Indigo
        Color(0xFF8B5CF6), // Violet
        Color(0xFF06B6D4), // Cyan
        Color(0xFF10B981), // Emerald
        Color(0xFFF59E0B), // Amber
        Color(0xFFEF4444), // Red
        Color(0xFFEC4899), // Pink
        Color(0xFF84CC16), // Lime
        Color(0xFF6B7280), // Gray
        Color(0xFF8B5A2B)  // Brown
    )
    return colors[index % colors.size]
}
