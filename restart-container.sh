#!/bin/bash

# Quick script to restart the QuickSpender container with fixed configuration

echo "🔄 Restarting QuickSpender container with database fix..."

# Configuration
REMOTE_HOST="${REMOTE_HOST:-***************}"
REMOTE_USER="${REMOTE_USER:-ad<PERSON><PERSON><PERSON>}"
SSH_KEY="${SSH_KEY:-C:/Users/<USER>/.ssh/id_rsa}"

# Prompt for sudo password if not set
if [ -z "$SUDO_PASSWORD" ]; then
    echo "🔐 Enter sudo password for $REMOTE_USER@$REMOTE_HOST:"
    read -s SUDO_PASSWORD
    export SUDO_PASSWORD
fi

echo "🔗 Connecting to server..."

ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" << EOF
    cd /home/<USER>/quickspender
    
    echo "🛑 Stopping current container..."
    echo "$SUDO_PASSWORD" | sudo -S docker-compose down
    
    echo "🔧 Updating .env file with correct Docker connection string..."
    sed -i 's/CONNECTION_STRING=.*/CONNECTION_STRING=Host=host.docker.internal;Port=5432;Database=quickspender;Username=quickspender;Password=************************************' .env
    
    echo "📋 Current .env configuration:"
    grep CONNECTION_STRING .env
    
    echo "🚀 Starting container with new configuration..."
    echo "$SUDO_PASSWORD" | sudo -S docker-compose up -d --build
    
    echo "⏳ Waiting for container to start..."
    sleep 15
    
    echo "📊 Container status:"
    echo "$SUDO_PASSWORD" | sudo -S docker-compose ps
    
    echo "📋 Recent container logs:"
    echo "$SUDO_PASSWORD" | sudo -S docker-compose logs --tail=20
    
    echo "🧪 Testing API endpoints..."
    sleep 5
    
    if curl -f http://localhost:8080/health 2>/dev/null; then
        echo "✅ Container API is responding!"
    else
        echo "❌ Container API still not responding"
        echo "Showing more logs:"
        echo "$SUDO_PASSWORD" | sudo -S docker-compose logs --tail=50
    fi
EOF

echo ""
echo "✅ Container restart completed!"
echo ""
echo "🌐 If successful, your API should be available at:"
echo "   • http://***************/quickspender/"
echo "   • http://***************/quickspender/health"
