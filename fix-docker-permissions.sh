#!/bin/bash

# Quick fix script for Docker permission issues

echo "🔧 Fixing Docker permission issues..."

# Configuration
REMOTE_HOST="${REMOTE_HOST:-***************}"
REMOTE_USER="${REMOTE_USER:-<PERSON><PERSON><PERSON><PERSON>}"
SSH_KEY="${SSH_KEY:-C:/Users/<USER>/.ssh/id_rsa}"

# Prompt for sudo password if not set
if [ -z "$SUDO_PASSWORD" ]; then
    echo "🔐 Enter sudo password for $REMOTE_USER@$REMOTE_HOST:"
    read -s SUDO_PASSWORD
    export SUDO_PASSWORD
fi

echo "🔗 Connecting to server..."

ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" << EOF
    echo "🔍 Checking current Docker status..."
    
    # Check if Docker is running
    if ! systemctl is-active --quiet docker; then
        echo "🚀 Starting Docker service..."
        echo "$SUDO_PASSWORD" | sudo -S systemctl start docker
        echo "$SUDO_PASSWORD" | sudo -S systemctl enable docker
    else
        echo "✅ Docker service is running"
    fi
    
    # Check if user is in docker group
    if ! groups \$USER | grep -q docker; then
        echo "👥 Adding user to docker group..."
        echo "$SUDO_PASSWORD" | sudo -S usermod -aG docker \$USER
        echo "✅ User added to docker group"
    else
        echo "✅ User is already in docker group"
    fi
    
    # Fix Docker socket permissions
    echo "🔧 Fixing Docker socket permissions..."
    echo "$SUDO_PASSWORD" | sudo -S chmod 666 /var/run/docker.sock
    
    # Test Docker access
    echo "🧪 Testing Docker access..."
    if docker info &> /dev/null; then
        echo "✅ Docker is accessible without sudo"
    else
        echo "⚠️  Docker still requires sudo (group membership needs logout/login)"
        echo "   Using sudo for Docker commands is normal for this session"
    fi
    
    # Check if system restart is required
    if [ -f /var/run/reboot-required ]; then
        echo ""
        echo "⚠️  SYSTEM RESTART REQUIRED"
        echo "   The server has pending updates that require a restart"
        echo "   This may be causing permission issues"
        echo ""
        echo "   To restart the server:"
        echo "   sudo reboot"
        echo ""
        echo "   After restart, Docker permissions should work normally"
    fi
    
    echo ""
    echo "🐳 Docker status:"
    echo "$SUDO_PASSWORD" | sudo -S docker --version
    echo "$SUDO_PASSWORD" | sudo -S docker-compose --version
    
    echo ""
    echo "📊 Current Docker containers:"
    echo "$SUDO_PASSWORD" | sudo -S docker ps -a || echo "No containers found"
EOF

echo ""
echo "✅ Docker permission fix completed!"
echo ""
echo "💡 Next steps:"
echo "   1. Try running the deployment script again: ./deploy.sh"
echo "   2. If issues persist, restart the server: ssh and run 'sudo reboot'"
echo "   3. After restart, run the deployment script again"
