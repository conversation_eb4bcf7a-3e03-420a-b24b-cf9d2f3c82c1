{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0/linux-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {}, ".NETCoreApp,Version=v8.0/linux-x64": {"QuickSpender.Domain/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": "8.0.0", "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64": "8.0.8"}, "runtime": {"QuickSpender.Domain.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64/8.0.8": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.824.36612"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "10.0.0.0", "fileVersion": "8.0.824.36612"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.824.36612"}, "netstandard.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "8.0.824.36612"}}, "native": {"createdump": {"fileVersion": "0.0.0.0"}, "libSystem.Globalization.Native.so": {"fileVersion": "0.0.0.0"}, "libSystem.IO.Compression.Native.so": {"fileVersion": "0.0.0.0"}, "libSystem.Native.so": {"fileVersion": "0.0.0.0"}, "libSystem.Net.Security.Native.so": {"fileVersion": "0.0.0.0"}, "libSystem.Security.Cryptography.Native.OpenSsl.so": {"fileVersion": "0.0.0.0"}, "libclrgc.so": {"fileVersion": "0.0.0.0"}, "libclrjit.so": {"fileVersion": "0.0.0.0"}, "libcoreclr.so": {"fileVersion": "0.0.0.0"}, "libcoreclrtraceptprovider.so": {"fileVersion": "0.0.0.0"}, "libhostfxr.so": {"fileVersion": "0.0.0.0"}, "libhostpolicy.so": {"fileVersion": "0.0.0.0"}, "libmscordaccore.so": {"fileVersion": "0.0.0.0"}, "libmscordbi.so": {"fileVersion": "0.0.0.0"}}}, "Microsoft.AspNetCore.Cryptography.Internal/8.0.0": {"runtime": {"lib/net8.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/8.0.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "8.0.0", "Microsoft.Extensions.Identity.Stores": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Identity.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.EntityFrameworkCore/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.0", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.0": {}, "Microsoft.EntityFrameworkCore.Relational/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Identity.Core/8.0.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.KeyDerivation": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Identity.Core.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Extensions.Identity.Stores/8.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.Identity.Core": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Identity.Stores.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Primitives/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}}}, "libraries": {"QuickSpender.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.linux-x64/8.0.8": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.Internal/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-buuMMCTxFcVkOkEftb2OafYxrveNGre9KJF4Oi1DkR4rxIj6oLam7Wq3g0Fp9hNVpJteKEPiupsxYnPrD/oUGA==", "path": "microsoft.aspnetcore.cryptography.internal/8.0.0", "hashPath": "microsoft.aspnetcore.cryptography.internal.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-65w93R5wqUUs35R9wjHHDf75GqAbxJsNByKZo5TbQOWSXcUbLWrDUWBQHv78iXIT0PL1pXNqKQz7OHiHMvo0/A==", "path": "microsoft.aspnetcore.cryptography.keyderivation/8.0.0", "hashPath": "microsoft.aspnetcore.cryptography.keyderivation.8.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.Identity.EntityFrameworkCore/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ua2LSZY/f0BkNUUVPPm83eq4Xnt+FZYutiMimRrzSmv2K2t2Ia/PuP4CfibYNSwnKl6fbZ49Bwn2mQGWnmmvOA==", "path": "microsoft.aspnetcore.identity.entityframeworkcore/8.0.0", "hashPath": "microsoft.aspnetcore.identity.entityframeworkcore.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SoODat83pGQUpWB9xULdMX6tuKpq/RTXDuJ2WeC1ldUKcKzLkaFJD1n+I0nOLY58odez/e7z8b6zdp235G/kyg==", "path": "microsoft.entityframeworkcore/8.0.0", "hashPath": "microsoft.entityframeworkcore.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR22s3+zoqlVI7xauFKn1znSIFHO8xuILT+noSwS8bZCKcHz0ydkTDQMuaxSa5WBaQrZmwtTz9rmRvJ7X8mSPQ==", "path": "microsoft.entityframeworkcore.abstractions/8.0.0", "hashPath": "microsoft.entityframeworkcore.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZXxEeLs2zoZ1TA+QoMMcw4f3Tirf8PzgdDax8RoWo0dxI2KmqiEGWYjhm2B/XyWfglc6+mNRyB8rZiQSmxCpeg==", "path": "microsoft.entityframeworkcore.analyzers/8.0.0", "hashPath": "microsoft.entityframeworkcore.analyzers.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fFKkr24cYc7Zw5T6DC4tEyOEPgPbq23BBmym1r9kn4ET9F3HKaetpOeQtV2RryYyUxEeNkJuxgfiZHTisqZc+A==", "path": "microsoft.entityframeworkcore.relational/8.0.0", "hashPath": "microsoft.entityframeworkcore.relational.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "path": "microsoft.extensions.caching.memory/8.0.0", "hashPath": "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Core/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hnXHyIQc+uc2uNMcIbr43+oNBAPEhMpW6lE8ux3MOegRz50WBna4AItlZDY7Y+Id1LLBbf73osUqeTw7CQ371w==", "path": "microsoft.extensions.identity.core/8.0.0", "hashPath": "microsoft.extensions.identity.core.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Identity.Stores/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DmDCpSpngZDBm44wVmxCeYs4HGJr/m32jMItp6pfb7KKtqWYw2vybHRg880j18k/eSFyM4v9uONsnEPgDdi9lg==", "path": "microsoft.extensions.identity.stores/8.0.0", "hashPath": "microsoft.extensions.identity.stores.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}}, "runtimes": {"android-x64": ["android", "linux-bionic-x64", "linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-bionic-x64": ["linux-bionic", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-musl-x64": ["linux-musl", "linux-x64", "linux", "unix-x64", "unix", "any", "base"], "linux-x64": ["linux", "unix-x64", "unix", "any", "base"]}}