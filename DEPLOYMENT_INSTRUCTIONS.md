# QuickSpender Deployment Instructions

## Prerequisites on VPS (Ubuntu)

### 1. <PERSON><PERSON><PERSON> (Already Installed)
```bash
# Verify nginx is running
sudo systemctl status nginx
```

### 2. PostgreSQL (Already Installed)
```bash
# Verify PostgreSQL is running
sudo systemctl status postgresql
```

### 3. Docker & Docker Compose (Already Installed)
```bash
# Verify Docker is running
docker --version
docker-compose --version
```

## Deployment Steps

### 1. Configure Environment Variables
```bash
# On your local machine, set these variables:
export REMOTE_HOST="your-vps-ip"
export REMOTE_USER="your-username"
export SSH_KEY="~/.ssh/id_rsa"
```

### 2. Update Configuration Files

#### Update .env file on server:
```bash
# After deployment, edit the .env file on your server
nano /home/<USER>/quickspender/.env

# Update these values:
DB_HOST=localhost
DB_PASSWORD=your-secure-password
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
DOMAIN=your-domain.com
```

#### Update Nginx site configuration:
```bash
# Edit the nginx site configuration
sudo nano /etc/nginx/sites-available/quickspender

# Replace "your-domain.com" with your actual domain
```

#### Update Android app configuration:
```xml
<!-- In android/app/src/main/res/values/config.xml -->
<string name="production_api_base_url">https://your-domain.com/api/</string>
```

### 3. Run Deployment Script
```bash
# Make script executable
chmod +x deploy.sh

# Run deployment
./deploy.sh
```

### 4. Setup SSL Certificate
```bash
# SSH to your server
ssh your-username@your-vps-ip

# Install SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Follow the prompts to complete SSL setup
```

### 5. Verify Deployment
```bash
# Check API health
curl https://your-domain.com/health

# Check services status
sudo systemctl status nginx
sudo systemctl status postgresql
docker-compose ps
```

## Configuration Details

### Database Setup
The deployment script will automatically:
- Create `quickspender` database
- Create `quickspender` user
- Grant necessary permissions

### Nginx Configuration
- HTTP traffic redirected to HTTPS
- Rate limiting enabled (10 requests/second)
- CORS headers configured for mobile app
- Security headers added
- Proxy to Docker container on port 8080

### Docker Container
- Runs on port 8080 (internal)
- Health checks enabled
- Auto-restart on failure
- Production environment

## Security Features

### HTTPS Only
- All HTTP traffic redirected to HTTPS
- SSL/TLS certificates via Let's Encrypt
- Security headers enabled

### Rate Limiting
- API rate limited to 10 requests/second per IP
- Burst allowance of 20 requests

### Database Security
- PostgreSQL with dedicated user
- Password-protected access
- Local connections only

### Container Security
- Non-root user in container
- Health checks enabled
- Resource limits (can be configured)

## Monitoring

### Health Endpoints
- API: `https://your-domain.com/health`
- Direct: `http://your-vps-ip:8080/health`

### Log Locations
- Nginx: `/var/log/nginx/`
- PostgreSQL: `/var/log/postgresql/`
- Docker: `docker-compose logs api`

### Service Status
```bash
# Check all services
sudo systemctl status nginx postgresql
docker-compose ps
```

## Troubleshooting

### Common Issues

1. **SSL Certificate Issues**
   ```bash
   # Check certificate status
   sudo certbot certificates
   
   # Renew certificate
   sudo certbot renew
   ```

2. **Database Connection Issues**
   ```bash
   # Check PostgreSQL status
   sudo systemctl status postgresql
   
   # Check database connectivity
   sudo -u postgres psql -c "\l"
   ```

3. **Docker Container Issues**
   ```bash
   # Check container logs
   docker-compose logs api
   
   # Restart container
   docker-compose restart api
   ```

4. **Nginx Issues**
   ```bash
   # Test nginx configuration
   sudo nginx -t
   
   # Reload nginx
   sudo systemctl reload nginx
   ```

### Debug Mode

To enable debug mode temporarily:
```bash
# Edit .env file
ASPNETCORE_ENVIRONMENT=Development

# Restart container
docker-compose restart api
```

## Maintenance

### Updates
```bash
# Pull latest code and redeploy
./deploy.sh
```

### Backup Database
```bash
# Create backup
sudo -u postgres pg_dump quickspender > backup_$(date +%Y%m%d).sql

# Restore backup
sudo -u postgres psql quickspender < backup_20231225.sql
```

### SSL Certificate Renewal
```bash
# Auto-renewal is configured, but you can manually renew:
sudo certbot renew
```

## Android App Configuration

### Debug Mode (Default)
- Uses `http://10.0.2.2:55348/api/` for Android emulator
- SSL certificate validation disabled
- HTTP logging enabled

### Production Mode
- Uses `https://your-domain.com/api/`
- Full SSL certificate validation
- No debug logging
- Code obfuscation enabled

### Build Commands
```bash
# Debug build
./gradlew assembleDebug

# Release build
./gradlew assembleRelease
```

## Support

If you encounter issues:
1. Check the logs (nginx, postgresql, docker)
2. Verify configuration files
3. Test endpoints manually
4. Check firewall settings
5. Verify DNS configuration

---

**Note**: Remember to update all placeholder values (your-domain.com, passwords, etc.) with your actual configuration before deployment.
