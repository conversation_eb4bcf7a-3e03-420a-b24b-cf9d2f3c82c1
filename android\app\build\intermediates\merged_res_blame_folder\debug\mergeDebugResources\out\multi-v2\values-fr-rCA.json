{"logs": [{"outputFile": "com.quickspender.android.app-mergeDebugResources-68:/values-fr-rCA/values-fr-rCA.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f011758bbd5e1f3717f8904866ac8535\\transformed\\appcompat-1.7.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,2855", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,2937"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,323,433,520,626,756,841,921,1012,1105,1203,1298,1398,1491,1584,1679,1770,1861,1947,2057,2168,2271,2382,2490,2597,2756,11128", "endColumns": "110,106,109,86,105,129,84,79,90,92,97,94,99,92,92,94,90,90,85,109,110,102,110,107,106,158,98,86", "endOffsets": "211,318,428,515,621,751,836,916,1007,1100,1198,1293,1393,1486,1579,1674,1765,1856,1942,2052,2163,2266,2377,2485,2592,2751,2850,11210"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ffc69b87a74cd983f3333cb07d0c197f\\transformed\\core-1.15.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "29,30,31,32,33,34,35,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2855,2953,3055,3154,3256,3360,3464,11518", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "2948,3050,3149,3251,3355,3459,3573,11614"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\668b07b585425ac50b3664ae01d10c25\\transformed\\foundation-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,94", "endOffsets": "138,233"}, "to": {"startLines": "113,114", "startColumns": "4,4", "startOffsets": "11889,11977", "endColumns": "87,94", "endOffsets": "11972,12067"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7e5c272ed8753b7bca75d20a7afd60fc\\transformed\\ui-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,390,496,583,663,757,849,936,1017,1102,1178,1253,1331,1405,1484,1553", "endColumns": "98,87,97,105,86,79,93,91,86,80,84,75,74,77,73,78,68,121", "endOffsets": "199,287,385,491,578,658,752,844,931,1012,1097,1173,1248,1326,1400,1479,1548,1670"}, "to": {"startLines": "36,37,38,39,40,41,42,100,101,102,103,105,106,107,108,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3578,3677,3765,3863,3969,4056,4136,10783,10875,10962,11043,11215,11291,11366,11444,11619,11698,11767", "endColumns": "98,87,97,105,86,79,93,91,86,80,84,75,74,77,73,78,68,121", "endOffsets": "3672,3760,3858,3964,4051,4131,4225,10870,10957,11038,11123,11286,11361,11439,11513,11693,11762,11884"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e6ee2619a776087357431d65cec3fd3a\\transformed\\material3-release\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,202,347,469,617,743,837,949,1091,1210,1369,1453,1554,1655,1756,1877,2012,2118,2268,2414,2550,2752,2881,2999,3122,3255,3357,3462,3586,3714,3816,3928,4033,4178,4330,4439,4548,4626,4719,4814,4932,5022,5108,5215,5295,5380,5477,5588,5681,5785,5873,5989,6090,6199,6321,6401,6511", "endColumns": "146,144,121,147,125,93,111,141,118,158,83,100,100,100,120,134,105,149,145,135,201,128,117,122,132,101,104,123,127,101,111,104,144,151,108,108,77,92,94,117,89,85,106,79,84,96,110,92,103,87,115,100,108,121,79,109,96", "endOffsets": "197,342,464,612,738,832,944,1086,1205,1364,1448,1549,1650,1751,1872,2007,2113,2263,2409,2545,2747,2876,2994,3117,3250,3352,3457,3581,3709,3811,3923,4028,4173,4325,4434,4543,4621,4714,4809,4927,5017,5103,5210,5290,5375,5472,5583,5676,5780,5868,5984,6085,6194,6316,6396,6506,6603"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4230,4377,4522,4644,4792,4918,5012,5124,5266,5385,5544,5628,5729,5830,5931,6052,6187,6293,6443,6589,6725,6927,7056,7174,7297,7430,7532,7637,7761,7889,7991,8103,8208,8353,8505,8614,8723,8801,8894,8989,9107,9197,9283,9390,9470,9555,9652,9763,9856,9960,10048,10164,10265,10374,10496,10576,10686", "endColumns": "146,144,121,147,125,93,111,141,118,158,83,100,100,100,120,134,105,149,145,135,201,128,117,122,132,101,104,123,127,101,111,104,144,151,108,108,77,92,94,117,89,85,106,79,84,96,110,92,103,87,115,100,108,121,79,109,96", "endOffsets": "4372,4517,4639,4787,4913,5007,5119,5261,5380,5539,5623,5724,5825,5926,6047,6182,6288,6438,6584,6720,6922,7051,7169,7292,7425,7527,7632,7756,7884,7986,8098,8203,8348,8500,8609,8718,8796,8889,8984,9102,9192,9278,9385,9465,9550,9647,9758,9851,9955,10043,10159,10260,10369,10491,10571,10681,10778"}}]}]}