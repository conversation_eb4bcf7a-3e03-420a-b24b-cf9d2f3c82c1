package com.quickspender.android.ui.screens.currency;

import com.quickspender.android.data.repository.CurrencyRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class CurrencySelectionViewModel_Factory implements Factory<CurrencySelectionViewModel> {
  private final Provider<CurrencyRepository> currencyRepositoryProvider;

  public CurrencySelectionViewModel_Factory(
      Provider<CurrencyRepository> currencyRepositoryProvider) {
    this.currencyRepositoryProvider = currencyRepositoryProvider;
  }

  @Override
  public CurrencySelectionViewModel get() {
    return newInstance(currencyRepositoryProvider.get());
  }

  public static CurrencySelectionViewModel_Factory create(
      Provider<CurrencyRepository> currencyRepositoryProvider) {
    return new CurrencySelectionViewModel_Factory(currencyRepositoryProvider);
  }

  public static CurrencySelectionViewModel newInstance(CurrencyRepository currencyRepository) {
    return new CurrencySelectionViewModel(currencyRepository);
  }
}
