1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.quickspender.android"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:6:22-76
13
14    <permission
14-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
15        android:name="com.quickspender.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
15-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
16        android:protectionLevel="signature" />
16-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
17
18    <uses-permission android:name="com.quickspender.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
18-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
18-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
19
20    <application
20-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:8:5-32:19
21        android:name="com.quickspender.android.QuickSpenderApplication"
21-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:9:9-48
22        android:allowBackup="true"
22-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:10:9-35
23        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
23-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\ffc69b87a74cd983f3333cb07d0c197f\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
24        android:dataExtractionRules="@xml/data_extraction_rules"
24-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:11:9-65
25        android:debuggable="true"
26        android:extractNativeLibs="false"
27        android:fullBackupContent="@xml/backup_rules"
27-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:12:9-54
28        android:icon="@mipmap/ic_launcher"
28-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:13:9-43
29        android:label="@string/app_name"
29-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:14:9-41
30        android:networkSecurityConfig="@xml/network_security_config"
30-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:19:9-69
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:15:9-54
32        android:supportsRtl="true"
32-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:16:9-35
33        android:testOnly="true"
34        android:theme="@style/Theme.QuickSpender"
34-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:17:9-50
35        android:usesCleartextTraffic="true" >
35-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:18:9-44
36        <activity
36-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:21:9-31:20
37            android:name="com.quickspender.android.MainActivity"
37-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:22:13-41
38            android:exported="true"
38-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:23:13-36
39            android:label="@string/app_name"
39-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:24:13-45
40            android:theme="@style/Theme.QuickSpender" >
40-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:25:13-54
41            <intent-filter>
41-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:26:13-30:29
42                <action android:name="android.intent.action.MAIN" />
42-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:27:17-69
42-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:27:25-66
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:29:17-77
44-->D:\Startups\QuickSpender\android\app\src\main\AndroidManifest.xml:29:27-74
45            </intent-filter>
46        </activity>
47        <activity
47-->[androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eace484f4aa5db8bdf18800217b1f015\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
48            android:name="androidx.compose.ui.tooling.PreviewActivity"
48-->[androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eace484f4aa5db8bdf18800217b1f015\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
49            android:exported="true" />
49-->[androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eace484f4aa5db8bdf18800217b1f015\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
50        <activity
50-->[androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e3c8b92233f242895d4710e348a84a0\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:23:9-25:39
51            android:name="androidx.activity.ComponentActivity"
51-->[androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e3c8b92233f242895d4710e348a84a0\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:24:13-63
52            android:exported="true" />
52-->[androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5e3c8b92233f242895d4710e348a84a0\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:25:13-36
53
54        <provider
54-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\340ed8e699a9abf713dcc38bc6e0f121\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
55            android:name="androidx.startup.InitializationProvider"
55-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\340ed8e699a9abf713dcc38bc6e0f121\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:25:13-67
56            android:authorities="com.quickspender.android.androidx-startup"
56-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\340ed8e699a9abf713dcc38bc6e0f121\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:26:13-68
57            android:exported="false" >
57-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\340ed8e699a9abf713dcc38bc6e0f121\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:27:13-37
58            <meta-data
58-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\340ed8e699a9abf713dcc38bc6e0f121\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
59-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\340ed8e699a9abf713dcc38bc6e0f121\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
60                android:value="androidx.startup" />
60-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\340ed8e699a9abf713dcc38bc6e0f121\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
61            <meta-data
61-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b90158aad7c8de2bb2b6ce50c566fed9\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.emoji2.text.EmojiCompatInitializer"
62-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b90158aad7c8de2bb2b6ce50c566fed9\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
63                android:value="androidx.startup" />
63-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b90158aad7c8de2bb2b6ce50c566fed9\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
64            <meta-data
64-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
65                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
66                android:value="androidx.startup" />
66-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
67        </provider>
68
69        <uses-library
69-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9f7368b08ea7780a30cc4aa9ee47ccf\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
70            android:name="androidx.window.extensions"
70-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9f7368b08ea7780a30cc4aa9ee47ccf\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
71            android:required="false" />
71-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9f7368b08ea7780a30cc4aa9ee47ccf\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
72        <uses-library
72-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9f7368b08ea7780a30cc4aa9ee47ccf\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
73            android:name="androidx.window.sidecar"
73-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9f7368b08ea7780a30cc4aa9ee47ccf\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
74            android:required="false" />
74-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\c9f7368b08ea7780a30cc4aa9ee47ccf\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
75
76        <service
76-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3037813a97b3cb97a9a746c0b34721d\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
77            android:name="androidx.room.MultiInstanceInvalidationService"
77-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3037813a97b3cb97a9a746c0b34721d\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
78            android:directBootAware="true"
78-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3037813a97b3cb97a9a746c0b34721d\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
79            android:exported="false" />
79-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a3037813a97b3cb97a9a746c0b34721d\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
80
81        <receiver
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
82            android:name="androidx.profileinstaller.ProfileInstallReceiver"
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
83            android:directBootAware="false"
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
84            android:enabled="true"
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
85            android:exported="true"
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
86            android:permission="android.permission.DUMP" >
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
87            <intent-filter>
87-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
88                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
88-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
89            </intent-filter>
90            <intent-filter>
90-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
91                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
91-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
92            </intent-filter>
93            <intent-filter>
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
94                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
95            </intent-filter>
96            <intent-filter>
96-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
97                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\2a65b45fa93f68791f98e6e07fc453e1\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
98            </intent-filter>
99        </receiver>
100    </application>
101
102</manifest>
