package com.quickspender.android.ui.screens.transactions;

import com.quickspender.android.data.repository.CurrencyRepository;
import com.quickspender.android.data.repository.TransactionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class TransactionListViewModel_Factory implements Factory<TransactionListViewModel> {
  private final Provider<TransactionRepository> transactionRepositoryProvider;

  private final Provider<CurrencyRepository> currencyRepositoryProvider;

  public TransactionListViewModel_Factory(
      Provider<TransactionRepository> transactionRepositoryProvider,
      Provider<CurrencyRepository> currencyRepositoryProvider) {
    this.transactionRepositoryProvider = transactionRepositoryProvider;
    this.currencyRepositoryProvider = currencyRepositoryProvider;
  }

  @Override
  public TransactionListViewModel get() {
    return newInstance(transactionRepositoryProvider.get(), currencyRepositoryProvider.get());
  }

  public static TransactionListViewModel_Factory create(
      Provider<TransactionRepository> transactionRepositoryProvider,
      Provider<CurrencyRepository> currencyRepositoryProvider) {
    return new TransactionListViewModel_Factory(transactionRepositoryProvider, currencyRepositoryProvider);
  }

  public static TransactionListViewModel newInstance(TransactionRepository transactionRepository,
      CurrencyRepository currencyRepository) {
    return new TransactionListViewModel(transactionRepository, currencyRepository);
  }
}
