#!/bin/bash

# QuickSpender Deployment Script
# This script deploys the QuickSpender application to a remote server via SSH

set -e  # Exit on any error

# Configuration
REMOTE_HOST="${REMOTE_HOST:-***************}"
REMOTE_USER="${REMOTE_USER:-<PERSON><PERSON><PERSON><PERSON>}"
REMOTE_PATH="${REMOTE_PATH:-/home/<USER>/quickspender}"
SSH_KEY="${SSH_KEY:-C:/Users/<USER>/.ssh/id_rsa}"

# Prompt for sudo password if not set
if [ -z "$SUDO_PASSWORD" ]; then
    echo "🔐 Enter sudo password for $REMOTE_USER@$REMOTE_HOST:"
    read -s SUDO_PASSWORD
    export SUDO_PASSWORD
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Cleanup function
cleanup() {
    if [ ! -z "$SSH_AGENT_PID" ]; then
        log_info "Cleaning up SSH agent..."
        kill $SSH_AGENT_PID 2>/dev/null || true
    fi
}

# Set trap to cleanup on exit
trap cleanup EXIT

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required variables are set
check_config() {
    log_info "Checking configuration..."
    
    if [ -z "$REMOTE_HOST" ] || [ "$REMOTE_HOST" = "your-vm-ip" ]; then
        log_error "Please set REMOTE_HOST environment variable or update the script"
        exit 1
    fi
    
    if [ -z "$REMOTE_USER" ] || [ "$REMOTE_USER" = "your-username" ]; then
        log_error "Please set REMOTE_USER environment variable or update the script"
        exit 1
    fi
    
    if [ ! -f "$SSH_KEY" ]; then
        log_error "SSH key not found at $SSH_KEY"
        exit 1
    fi

    # Start ssh-agent and add key to avoid repeated passphrase prompts
    log_info "Setting up SSH agent to avoid repeated passphrase prompts..."
    eval "$(ssh-agent -s)" > /dev/null

    # Add key to agent (you'll be prompted for passphrase once)
    log_info "Adding SSH key to agent (enter passphrase when prompted)..."
    if ssh-add "$SSH_KEY"; then
        log_success "SSH key added to agent successfully"
    else
        log_warning "Failed to add SSH key to agent. You may be prompted for passphrase multiple times."
    fi
    
    log_success "Configuration check passed"
}

# Install Docker and Docker Compose on remote server
install_docker() {
    log_info "Checking and installing Docker requirements on remote server..."

    ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" << EOF
        # Check if Docker is installed
        if ! command -v docker &> /dev/null; then
            echo "❌ Docker is not installed. Installing Docker..."
            echo "$SUDO_PASSWORD" | sudo -S apt update
            echo "$SUDO_PASSWORD" | sudo -S apt install -y docker.io docker-compose
            echo "$SUDO_PASSWORD" | sudo -S systemctl enable docker
            echo "$SUDO_PASSWORD" | sudo -S systemctl start docker
            echo "$SUDO_PASSWORD" | sudo -S usermod -aG docker $USER
            echo "✅ Docker installed successfully"
            DOCKER_INSTALLED=true
        else
            echo "✅ Docker is already installed"
            DOCKER_INSTALLED=false
        fi

        # Check if user is in docker group
        if ! groups $USER | grep -q docker; then
            echo "❌ User not in docker group. Adding user to docker group..."
            echo "$SUDO_PASSWORD" | sudo -S usermod -aG docker $USER
            DOCKER_INSTALLED=true
        fi

        # Check if Docker Compose is installed
        if ! command -v docker-compose &> /dev/null; then
            echo "❌ Docker Compose is not installed. Installing..."
            echo "$SUDO_PASSWORD" | sudo -S apt install -y docker-compose
            echo "✅ Docker Compose installed successfully"
        else
            echo "✅ Docker Compose is already installed"
        fi

        # Verify Docker is running
        if ! echo "$SUDO_PASSWORD" | sudo -S docker info &> /dev/null; then
            echo "Starting Docker service..."
            echo "$SUDO_PASSWORD" | sudo -S systemctl start docker
            sleep 5
        fi

        # If we just installed Docker or added user to group, we need to use sudo for Docker commands
        if [ "$DOCKER_INSTALLED" = "true" ]; then
            echo "⚠️  Docker group membership requires logout/login or using sudo for this session"
            echo "Using sudo for Docker commands in this deployment..."
            USE_SUDO_DOCKER=true
        else
            # Test if user can run docker without sudo
            if docker info &> /dev/null; then
                echo "✅ User can run Docker commands without sudo"
                USE_SUDO_DOCKER=false
            else
                echo "⚠️  User cannot run Docker without sudo, using sudo for Docker commands"
                USE_SUDO_DOCKER=true
            fi
        fi

        if [ "$USE_SUDO_DOCKER" = "true" ]; then
            echo "Docker version: \$(echo '$SUDO_PASSWORD' | sudo -S docker --version)"
            echo "Docker Compose version: \$(echo '$SUDO_PASSWORD' | sudo -S docker-compose --version)"
        else
            echo "Docker version: \$(docker --version)"
            echo "Docker Compose version: \$(docker-compose --version)"
        fi
EOF

    log_success "Docker requirements verified"
}

# Test SSH connection
test_ssh() {
    log_info "Testing SSH connection to $REMOTE_USER@$REMOTE_HOST..."
    
    if ssh -i "$SSH_KEY" -o ConnectTimeout=10 "$REMOTE_USER@$REMOTE_HOST" "echo 'SSH connection successful'" > /dev/null 2>&1; then
        log_success "SSH connection successful"
    else
        log_error "SSH connection failed"
        exit 1
    fi
}

# Build the application for Linux deployment
build_application() {
    log_info "Building QuickSpender application for Linux deployment..."

    # Check if .NET is installed
    if ! command -v dotnet &> /dev/null; then
        log_error ".NET SDK is not installed"
        exit 1
    fi

    # Clean previous builds
    log_info "Cleaning previous builds..."
    dotnet clean

    # Restore dependencies
    log_info "Restoring dependencies..."
    dotnet restore

    # Publish for Linux x64 (self-contained)
    log_info "Publishing application for linux-x64..."
    dotnet publish -c Release -r linux-x64 --self-contained -o ./publish/linux-x64

    # Create compressed archive for faster transfer
    log_info "Creating compressed archive..."
    if command -v tar &> /dev/null; then
        tar -czf quickspender-linux.tar.gz -C ./publish/linux-x64 .
    elif command -v 7z &> /dev/null; then
        7z a -tgzip quickspender-linux.tar.gz ./publish/linux-x64/*
    elif command -v powershell &> /dev/null; then
        # Windows PowerShell compression as fallback
        powershell -Command "Compress-Archive -Path './publish/linux-x64/*' -DestinationPath 'quickspender-linux.zip' -Force"
        mv quickspender-linux.zip quickspender-linux.tar.gz
        log_warning "Used PowerShell compression. Archive is actually a ZIP file."
    else
        log_error "No compression tool found. Please install tar, 7z, or use PowerShell on Windows."
        log_info "Available options:"
        log_info "  - Windows: Install Git Bash (includes tar) or 7-Zip"
        log_info "  - Linux/macOS: tar should be available by default"
        exit 1
    fi

    log_success "Application built and compressed successfully"
}

# Cleanup local files
cleanup_local() {
    log_info "Cleaning up local build files..."

    # Remove the compressed archive
    if [ -f "quickspender-linux.tar.gz" ]; then
        rm -f quickspender-linux.tar.gz
        log_info "Removed local compressed archive"
    fi

    # Remove the publish directory
    if [ -d "./publish" ]; then
        rm -rf ./publish
        log_info "Removed local publish directory"
    fi

    log_success "Local cleanup completed"
}

# Create remote directory
create_remote_dir() {
    log_info "Creating remote directory structure..."
    
    ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" "
        mkdir -p $REMOTE_PATH
        mkdir -p $REMOTE_PATH/ssl
    "
    
    log_success "Remote directory created"
}

# Copy files to remote server
copy_files() {
    log_info "Copying files to remote server..."

    # Copy the compressed application archive
    if [ -f "quickspender-linux.tar.gz" ]; then
        log_info "Copying compressed application archive..."
        scp -i "$SSH_KEY" "quickspender-linux.tar.gz" "$REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/"
    else
        log_error "Compressed application archive not found. Did the build succeed?"
        exit 1
    fi

    # Copy configuration files
    CONFIG_FILES=(
        "docker-compose.yml"
        "Dockerfile"
        "nginx-site.conf"
        ".env.example"
    )

    for file in "${CONFIG_FILES[@]}"; do
        if [ -e "$file" ]; then
            log_info "Copying $file..."
            scp -i "$SSH_KEY" -r "$file" "$REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/"
        else
            log_warning "File $file not found, skipping..."
        fi
    done

    # Extract and cleanup on remote server
    log_info "Extracting application on remote server..."
    ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" << EOF
        cd $REMOTE_PATH

        # Create app directory if it doesn't exist
        mkdir -p app

        # Detect file type and extract accordingly
        if [ -f "quickspender-linux.tar.gz" ]; then
            # Check if it's actually a tar.gz or a zip file
            if file quickspender-linux.tar.gz | grep -q "gzip"; then
                echo "Extracting tar.gz archive..."
                tar -xzf quickspender-linux.tar.gz -C app/
            else
                echo "File appears to be ZIP format, extracting with unzip..."
                unzip -q quickspender-linux.tar.gz -d app/
            fi

            # Remove the compressed file to save space
            rm -f quickspender-linux.tar.gz
        else
            echo "Error: No compressed archive found!"
            exit 1
        fi

        # Make the application executable
        chmod +x app/QuickSpender.API || true

        # Verify the application binary
        if [ -f "app/QuickSpender.API" ]; then
            echo "✅ Application binary found and made executable"
            ls -la app/QuickSpender.API
        else
            echo "❌ Application binary not found after extraction!"
            ls -la app/
            exit 1
        fi

        echo "Application extracted successfully"
EOF
    
    log_success "Files copied successfully"
}

# Setup environment file
setup_env() {
    log_info "Setting up environment file..."
    
    ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" << EOF
        cd $REMOTE_PATH
        if [ ! -f .env ]; then
            cp .env.example .env
            echo '✅ Created .env file from .env.example'
        else
            echo '✅ .env file already exists'
        fi

        # Validate that .env has required variables
        if grep -q "your-super-secret" .env; then
            echo "⚠️  WARNING: .env file contains default values. Please update:"
            echo "   - JWT_SECRET"
            echo "   - DB_PASSWORD"
        else
            echo "✅ .env file appears to be configured"
        fi

        # Fix connection string for Docker container access (will be updated after database setup)
        echo "🔧 Preparing .env file for Docker container..."
        sed -i 's/DB_HOST=.*/DB_HOST=host.docker.internal/' .env
        echo "✅ Basic database configuration prepared"

        # Show current .env configuration (without secrets)
        echo "Current .env configuration:"
        grep -E "^(ASPNETCORE_ENVIRONMENT|API_PORT|DB_HOST|DB_NAME|DB_USER|JWT_ISSUER|CONNECTION_STRING)" .env || echo "No configuration found"

        # Test database connection from host
        echo "Testing database connection from host..."
        if echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -c "SELECT 1;" quickspender &>/dev/null; then
            echo "✅ Database connection successful from host"
        else
            echo "❌ Database connection failed from host - this may cause container restart issues"
        fi
EOF
    
    log_success "Environment file setup complete"
}

# Setup Nginx configuration
setup_nginx() {
    log_info "Setting up Nginx configuration..."

    ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" << EOF
        # Copy nginx site configuration
        echo "$SUDO_PASSWORD" | sudo -S cp $REMOTE_PATH/nginx-site.conf /etc/nginx/sites-available/quickspender

        # Enable the site
        echo "$SUDO_PASSWORD" | sudo -S ln -sf /etc/nginx/sites-available/quickspender /etc/nginx/sites-enabled/

        # Remove default site if it exists
        echo "$SUDO_PASSWORD" | sudo -S rm -f /etc/nginx/sites-enabled/default

        # Test nginx configuration
        echo "$SUDO_PASSWORD" | sudo -S nginx -t

        # Reload nginx
        echo "$SUDO_PASSWORD" | sudo -S systemctl reload nginx
EOF

    log_success "Nginx configuration setup complete"
}

# Setup SSL certificate (skipped for IP-based deployment)
setup_ssl() {
    log_info "Skipping SSL setup for IP-based deployment..."
    log_info "API will be available at: http://***************/quickspender/"
    log_warning "Note: Using HTTP (not HTTPS) for IP-based access"
    log_success "SSL setup skipped"
}

# Setup PostgreSQL database
setup_database() {
    log_info "Setting up PostgreSQL database..."

    ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" << EOF
        # Use postgres admin password to create database user with specified password
        DB_USER_PASSWORD="************"
        echo "🔐 Creating database user with password: \$DB_USER_PASSWORD"

        # Create fresh database and user using postgres admin password
        echo "🗄️  Creating fresh database and user..."
        PGPASSWORD="d9th0I1ajnXr2N2inE94zyzC796SceJcukq" psql -h localhost -U postgres -c "CREATE DATABASE quickspender;"
        PGPASSWORD="d9th0I1ajnXr2N2inE94zyzC796SceJcukq" psql -h localhost -U postgres -c "CREATE USER quickspender WITH PASSWORD '************';"
        PGPASSWORD="d9th0I1ajnXr2N2inE94zyzC796SceJcukq" psql -h localhost -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE quickspender TO quickspender;"
        PGPASSWORD="d9th0I1ajnXr2N2inE94zyzC796SceJcukq" psql -h localhost -U postgres -c "ALTER USER quickspender CREATEDB;"
        PGPASSWORD="d9th0I1ajnXr2N2inE94zyzC796SceJcukq" psql -h localhost -U postgres -d quickspender -c "GRANT ALL ON SCHEMA public TO quickspender;"
        echo "✅ Fresh database and user created successfully"

        # Save the password for later use in .env file
        echo "************" > /tmp/db_password.txt

        # Configure PostgreSQL for Docker container access
        echo "🔧 Configuring PostgreSQL for Docker container access..."
        PG_VERSION=\$(echo "$SUDO_PASSWORD" | sudo -S -u postgres psql -tAc "SELECT version();" | grep -oP 'PostgreSQL \\K[0-9]+')
        PG_CONF_FILE="/etc/postgresql/\$PG_VERSION/main/postgresql.conf"
        PG_HBA_FILE="/etc/postgresql/\$PG_VERSION/main/pg_hba.conf"

        # Enable listening on all addresses
        if [ -f "\$PG_CONF_FILE" ]; then
            if ! grep -q "listen_addresses = '\\*'" "\$PG_CONF_FILE"; then
                echo "$SUDO_PASSWORD" | sudo -S sed -i "s/#listen_addresses = 'localhost'/listen_addresses = '*'/" "\$PG_CONF_FILE"
                echo "$SUDO_PASSWORD" | sudo -S sed -i "s/listen_addresses = 'localhost'/listen_addresses = '*'/" "\$PG_CONF_FILE"
                echo "✅ PostgreSQL configured to listen on all addresses"
                RESTART_PG=true
            fi
        fi

        # Add Docker container access rules
        if [ -f "\$PG_HBA_FILE" ]; then
            # Add rule for Docker containers (172.x.x.x network)
            if ! grep -q "host.*quickspender.*quickspender.**********/8.*md5" "\$PG_HBA_FILE"; then
                echo "$SUDO_PASSWORD" | sudo -S sh -c "echo 'host    quickspender    quickspender    *********/8            md5' >> \$PG_HBA_FILE"
                echo "✅ Added Docker container access rule"
                RESTART_PG=true
            fi

            # Add rule for host.docker.internal access
            if ! grep -q "host.*quickspender.*quickspender.*0.0.0.0/0.*md5" "\$PG_HBA_FILE"; then
                echo "$SUDO_PASSWORD" | sudo -S sh -c "echo 'host    quickspender    quickspender    0.0.0.0/0               md5' >> \$PG_HBA_FILE"
                echo "✅ Added host.docker.internal access rule"
                RESTART_PG=true
            fi
        fi

        # Restart PostgreSQL if configuration changed
        if [ "\$RESTART_PG" = "true" ]; then
            echo "🔄 Restarting PostgreSQL to apply configuration changes..."
            echo "$SUDO_PASSWORD" | sudo -S systemctl restart postgresql
            sleep 5
            echo "✅ PostgreSQL restarted"
        fi

        # Test database connection with the exact credentials the container will use
        echo "🧪 Testing database connection with new user password..."
        export PGPASSWORD="************"
        if psql -h localhost -p 5432 -U quickspender -d quickspender -c "SELECT 1;" &>/dev/null; then
            echo "✅ Database authentication successful with new user password"
        else
            echo "❌ Database authentication failed with new user password"
            echo "This will cause the container to restart continuously"

            # Debug information
            echo "Debug: Testing postgres admin connection..."
            PGPASSWORD="d9th0I1ajnXr2N2inE94zyzC796SceJcukq" psql -h localhost -U postgres -c "\\du" | grep quickspender || echo "User not found"
            PGPASSWORD="d9th0I1ajnXr2N2inE94zyzC796SceJcukq" psql -h localhost -U postgres -c "\\l" | grep quickspender || echo "Database not found"
        fi
        unset PGPASSWORD
        rm -f /tmp/db_password.txt
EOF

    log_success "Database setup complete"

    # Update .env file with the generated password
    log_info "Updating .env file with generated database password..."

    ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" << EOF
        cd $REMOTE_PATH

        # Get the generated password
        if [ -f /tmp/db_password.txt ]; then
            DB_USER_PASSWORD=\$(cat /tmp/db_password.txt)
            echo "🔧 Updating configuration files with new password..."

            # Update .env file with new user password
            sed -i "s/CONNECTION_STRING=.*/CONNECTION_STRING=Host=host.docker.internal;Port=5432;Database=quickspender;Username=quickspender;Password=************/" .env
            sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=************/" .env

            # Update appsettings.json with correct connection string
            if [ -f app/appsettings.json ]; then
                echo "🔧 Updating appsettings.json..."
                sed -i 's/"DefaultConnection": ".*"/"DefaultConnection": "Host=host.docker.internal;Port=5432;Database=quickspender;Username=quickspender;Password=************"/' app/appsettings.json
                echo "✅ appsettings.json updated"
            fi

            # Also check for appsettings.Production.json
            if [ -f app/appsettings.Production.json ]; then
                echo "🔧 Updating appsettings.Production.json..."
                sed -i 's/"DefaultConnection": ".*"/"DefaultConnection": "Host=host.docker.internal;Port=5432;Database=quickspender;Username=quickspender;Password=************"/' app/appsettings.Production.json
                echo "✅ appsettings.Production.json updated"
            fi

            echo "✅ Configuration files updated with new user password"
            echo "New connection string: Host=host.docker.internal;Port=5432;Database=quickspender;Username=quickspender;Password=************"

            # Clean up password file
            rm -f /tmp/db_password.txt
        else
            echo "❌ Password file not found, using existing configuration"
        fi
EOF

    log_success "Environment configuration updated"
}

# Deploy application
deploy_app() {
    log_info "Deploying application..."

    ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" << EOF
        cd $REMOTE_PATH

        # Stop existing containers using sudo
        echo "Stopping existing containers..."
        echo "$SUDO_PASSWORD" | sudo -S docker-compose down || true

        # Remove old images to free space
        echo "Cleaning up old Docker images..."
        echo "$SUDO_PASSWORD" | sudo -S docker system prune -f

        # Ensure Docker is running
        if ! systemctl is-active --quiet docker; then
            echo "Starting Docker service..."
            echo "$SUDO_PASSWORD" | sudo -S systemctl start docker
            sleep 5
        fi

        echo "🔐 Using sudo for all Docker commands for reliable deployment"

        # Build and start containers with verbose output
        echo "Building and starting containers..."
        if echo "$SUDO_PASSWORD" | sudo -S docker-compose up -d --build --force-recreate; then
            echo "✅ Docker Compose started successfully"
        else
            echo "❌ Docker Compose failed to start"
            echo "Showing Docker Compose logs:"
            echo "$SUDO_PASSWORD" | sudo -S docker-compose logs --tail=100
            exit 1
        fi

        # Check if container started successfully
        echo "Checking container status..."
        echo "$SUDO_PASSWORD" | sudo -S docker-compose ps

        # Show container logs if there are issues
        CONTAINER_STATUS=\$(echo "$SUDO_PASSWORD" | sudo -S docker-compose ps | grep quickspender-api | awk '{print \$4}')
        if [[ "\$CONTAINER_STATUS" == "Restarting" ]]; then
            echo "⚠️  Container is restarting - likely due to application errors"
            echo "Showing container logs to diagnose the issue:"
            echo "$SUDO_PASSWORD" | sudo -S docker-compose logs --tail=100
            echo ""
            echo "Common causes:"
            echo "1. Database connection issues"
            echo "2. Missing environment variables"
            echo "3. Application startup errors"
            echo ""
            echo "Container will continue restarting until the issue is fixed"
        elif ! echo "$SUDO_PASSWORD" | sudo -S docker-compose ps | grep -q "Up"; then
            echo "❌ Container failed to start. Showing logs:"
            echo "$SUDO_PASSWORD" | sudo -S docker-compose logs --tail=50
            echo "Checking Docker daemon status:"
            echo "$SUDO_PASSWORD" | sudo -S docker info
            exit 1
        else
            echo "✅ Container is running"
        fi

        # Wait for services to be healthy and check for restart loops
        echo 'Waiting for services to start...'
        sleep 30

        # Check if container is still restarting after wait
        FINAL_STATUS=\$(echo "$SUDO_PASSWORD" | sudo -S docker-compose ps | grep quickspender-api | awk '{print \$4}')
        if [[ "\$FINAL_STATUS" == "Restarting" ]]; then
            echo "❌ Container is still restarting after 30 seconds"
            echo "This indicates a persistent application error"
            echo ""
            echo "🔧 Troubleshooting steps:"
            echo "1. Check database connection"
            echo "2. Verify .env file configuration"
            echo "3. Run: ./fix-database.sh"
            echo ""
            echo "Recent container logs:"
            echo "$SUDO_PASSWORD" | sudo -S docker-compose logs --tail=20
        fi

        # Final status check
        echo "Final container status:"
        echo "$SUDO_PASSWORD" | sudo -S docker-compose ps

        # Show resource usage
        echo "Container resource usage:"
        echo "$SUDO_PASSWORD" | sudo -S docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" || echo "Could not get stats"

        # Final verification
        echo "=== DEPLOYMENT VERIFICATION ==="
        echo "Docker Compose status:"
        echo "$SUDO_PASSWORD" | sudo -S docker-compose ps
        echo ""
        echo "Container logs (last 10 lines):"
        echo "$SUDO_PASSWORD" | sudo -S docker-compose logs --tail=10
        echo ""
        echo "Testing API endpoints:"
        sleep 5

        # Test direct container health
        if curl -f http://localhost:8080/health 2>/dev/null; then
            echo "✅ Container API is responding"
        else
            echo "❌ Container API health check failed"
        fi

        # Test database connection from container
        echo "🗄️  Testing database connection from container..."
        if echo "$SUDO_PASSWORD" | sudo -S docker exec quickspender-api sh -c "curl -f http://localhost:8080/health" 2>/dev/null; then
            echo "✅ Container can connect to database (health check passed)"
        else
            echo "❌ Container database connection failed"
            echo "Checking container logs for database errors:"
            echo "$SUDO_PASSWORD" | sudo -S docker-compose logs --tail=10 | grep -i "database\\|connection\\|postgres"
        fi

        # Test nginx proxy
        if curl -f http://localhost/quickspender/health 2>/dev/null; then
            echo "✅ Nginx proxy is working"
        else
            echo "❌ Nginx proxy health check failed"
        fi

        # Test main API endpoint
        if curl -f http://localhost/quickspender/ 2>/dev/null; then
            echo "✅ Main API endpoint is accessible"
        else
            echo "❌ Main API endpoint failed"
        fi
EOF

    log_success "Application deployed successfully"
}

# Check deployment health
check_health() {
    log_info "Checking deployment health..."

    ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" "
        cd $REMOTE_PATH

        # Check if containers are running
        if docker-compose ps | grep -q 'Up'; then
            echo 'API container is running'

            # Test API health endpoint
            sleep 10
            if curl -f http://localhost:8080/health > /dev/null 2>&1; then
                echo 'API health check passed'
            else
                echo 'API health check failed'
                docker-compose logs api
            fi

            # Test HTTPS endpoint if available
            if curl -f -k https://localhost/health > /dev/null 2>&1; then
                echo 'HTTPS endpoint is working'
            else
                echo 'HTTPS endpoint check failed (this is normal if SSL is not configured yet)'
            fi
        else
            echo 'API container is not running'
            docker-compose ps
            docker-compose logs
        fi

        # Check nginx status
        if systemctl is-active --quiet nginx; then
            echo 'Nginx is running'
        else
            echo 'Nginx is not running'
        fi

        # Check PostgreSQL status
        if systemctl is-active --quiet postgresql; then
            echo 'PostgreSQL is running'
        else
            echo 'PostgreSQL is not running'
        fi
    "

    log_success "Health check complete"
}

# Main deployment process
main() {
    log_info "Starting QuickSpender deployment..."

    check_config
    build_application
    test_ssh
    install_docker
    create_remote_dir
    copy_files
    setup_env
    setup_database
    setup_nginx
    #setup_ssl
    deploy_app
    check_health
    cleanup_local

    log_success "Deployment completed successfully!"
    log_success "🚀 QuickSpender API is now running!"
    echo ""
    log_info "📍 API Endpoints:"
    log_info "   • Main API: http://***************/quickspender/"
    log_info "   • Direct API: http://***************/api/"
    log_info "   • Health Check: http://***************/quickspender/health"
    log_info "   • Server Status: http://***************/"

    # Check if system restart is required
    ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" "
        if [ -f /var/run/reboot-required ]; then
            echo '⚠️  SYSTEM RESTART REQUIRED: The server needs to be restarted'
            echo '   This may be causing Docker permission issues'
            echo '   Consider running: sudo reboot'
            echo '   After restart, run this deployment script again'
        fi
    "
    echo ""
    log_info "🔧 Technical Details:"
    log_info "   • Container Port: 8080"
    log_info "   • Database: PostgreSQL on port 5432"
    log_info "   • Resource Limits: 1GB RAM, 1 CPU core"
    echo ""
    log_info "📱 For Android App Configuration:"
    log_info "   • Base URL: http://***************/quickspender/"
    log_info "   • API URL: http://***************/quickspender/api/"
}

# Run main function
main "$@"
