#!/bin/bash

# QuickSpender Deployment Script
# This script deploys the QuickSpender application to a remote server via SSH

set -e  # Exit on any error

# Configuration
REMOTE_HOST="${REMOTE_HOST:-***************}"
REMOTE_USER="${REMOTE_USER:-<PERSON><PERSON><PERSON><PERSON>}"
REMOTE_PATH="${REMOTE_PATH:-/home/<USER>/quickspender}"
SSH_KEY="${SSH_KEY:-C:/Users/<USER>/.ssh/id_rsa}"

# Prompt for sudo password if not set
if [ -z "$SUDO_PASSWORD" ]; then
    echo "🔐 Enter sudo password for $REMOTE_USER@$REMOTE_HOST:"
    read -s SUDO_PASSWORD
    export SUDO_PASSWORD
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Cleanup function
cleanup() {
    if [ ! -z "$SSH_AGENT_PID" ]; then
        log_info "Cleaning up SSH agent..."
        kill $SSH_AGENT_PID 2>/dev/null || true
    fi
}

# Set trap to cleanup on exit
trap cleanup EXIT

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required variables are set
check_config() {
    log_info "Checking configuration..."
    
    if [ -z "$REMOTE_HOST" ] || [ "$REMOTE_HOST" = "your-vm-ip" ]; then
        log_error "Please set REMOTE_HOST environment variable or update the script"
        exit 1
    fi
    
    if [ -z "$REMOTE_USER" ] || [ "$REMOTE_USER" = "your-username" ]; then
        log_error "Please set REMOTE_USER environment variable or update the script"
        exit 1
    fi
    
    if [ ! -f "$SSH_KEY" ]; then
        log_error "SSH key not found at $SSH_KEY"
        exit 1
    fi

    # Start ssh-agent and add key to avoid repeated passphrase prompts
    log_info "Setting up SSH agent to avoid repeated passphrase prompts..."
    eval "$(ssh-agent -s)" > /dev/null

    # Add key to agent (you'll be prompted for passphrase once)
    log_info "Adding SSH key to agent (enter passphrase when prompted)..."
    if ssh-add "$SSH_KEY"; then
        log_success "SSH key added to agent successfully"
    else
        log_warning "Failed to add SSH key to agent. You may be prompted for passphrase multiple times."
    fi
    
    log_success "Configuration check passed"
}

# Test SSH connection
test_ssh() {
    log_info "Testing SSH connection to $REMOTE_USER@$REMOTE_HOST..."
    
    if ssh -i "$SSH_KEY" -o ConnectTimeout=10 "$REMOTE_USER@$REMOTE_HOST" "echo 'SSH connection successful'" > /dev/null 2>&1; then
        log_success "SSH connection successful"
    else
        log_error "SSH connection failed"
        exit 1
    fi
}

# Create remote directory
create_remote_dir() {
    log_info "Creating remote directory structure..."
    
    ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" "
        mkdir -p $REMOTE_PATH
        mkdir -p $REMOTE_PATH/ssl
    "
    
    log_success "Remote directory created"
}

# Copy files to remote server
copy_files() {
    log_info "Copying files to remote server..."
    
    # Files to copy
    FILES=(
        "docker-compose.yml"
        "Dockerfile"
        "nginx-site.conf"
        ".env.example"
        "src/"
        "QuickSpender.sln"
    )
    
    for file in "${FILES[@]}"; do
        if [ -e "$file" ]; then
            log_info "Copying $file..."
            scp -i "$SSH_KEY" -r "$file" "$REMOTE_USER@$REMOTE_HOST:$REMOTE_PATH/"
        else
            log_warning "File $file not found, skipping..."
        fi
    done
    
    log_success "Files copied successfully"
}

# Setup environment file
setup_env() {
    log_info "Setting up environment file..."
    
    ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" "
        cd $REMOTE_PATH
        if [ ! -f .env ]; then
            cp .env.example .env
            echo 'Please update the .env file with your configuration'
        fi
    "
    
    log_success "Environment file setup complete"
}

# Setup Nginx configuration
setup_nginx() {
    log_info "Setting up Nginx configuration..."

    ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" << EOF
        # Copy nginx site configuration
        echo "$SUDO_PASSWORD" | sudo -S cp $REMOTE_PATH/nginx-site.conf /etc/nginx/sites-available/quickspender

        # Enable the site
        echo "$SUDO_PASSWORD" | sudo -S ln -sf /etc/nginx/sites-available/quickspender /etc/nginx/sites-enabled/

        # Remove default site if it exists
        echo "$SUDO_PASSWORD" | sudo -S rm -f /etc/nginx/sites-enabled/default

        # Test nginx configuration
        echo "$SUDO_PASSWORD" | sudo -S nginx -t

        # Reload nginx
        echo "$SUDO_PASSWORD" | sudo -S systemctl reload nginx
EOF

    log_success "Nginx configuration setup complete"
}

# Setup SSL certificate (Let's Encrypt)
setup_ssl() {
    log_info "Setting up SSL certificate..."

    ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" << EOF
        # Install certbot if not present
        if ! command -v certbot &> /dev/null; then
            echo "$SUDO_PASSWORD" | sudo -S apt update
            echo "$SUDO_PASSWORD" | sudo -S apt install -y certbot python3-certbot-nginx
        fi

        # Note: You'll need to run this manually with your domain
        echo 'To setup SSL certificate, run:'
        echo 'sudo certbot --nginx -d your-domain.com -d www.your-domain.com'
        echo 'Make sure to update the domain in nginx-site.conf first'
EOF

    log_success "SSL setup instructions provided"
}

# Setup PostgreSQL database
setup_database() {
    log_info "Setting up PostgreSQL database..."

    ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" "
        # Create database and user if they don't exist
        echo '$SUDO_PASSWORD' | sudo -S -u postgres psql -c \"CREATE DATABASE quickspender;\" 2>/dev/null || echo 'Database already exists'
        echo '$SUDO_PASSWORD' | sudo -S -u postgres psql -c \"CREATE USER quickspender WITH PASSWORD 'QuickSpender123!';\" 2>/dev/null || echo 'User already exists'
        echo '$SUDO_PASSWORD' | sudo -S -u postgres psql -c \"GRANT ALL PRIVILEGES ON DATABASE quickspender TO quickspender;\"
        echo '$SUDO_PASSWORD' | sudo -S -u postgres psql -c \"ALTER USER quickspender CREATEDB;\"
    "

    log_success "Database setup complete"
}

# Deploy application
deploy_app() {
    log_info "Deploying application..."
    
    ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" "
        cd $REMOTE_PATH
        
        # Stop existing containers
        docker-compose down || true
        
        # Remove old images
        docker system prune -f
        
        # Build and start containers
        docker-compose up -d --build
        
        # Wait for services to be healthy
        echo 'Waiting for services to start...'
        sleep 30
        
        # Check service status
        docker-compose ps
    "
    
    log_success "Application deployed successfully"
}

# Check deployment health
check_health() {
    log_info "Checking deployment health..."

    ssh -i "$SSH_KEY" "$REMOTE_USER@$REMOTE_HOST" "
        cd $REMOTE_PATH

        # Check if containers are running
        if docker-compose ps | grep -q 'Up'; then
            echo 'API container is running'

            # Test API health endpoint
            sleep 10
            if curl -f http://localhost:8080/health > /dev/null 2>&1; then
                echo 'API health check passed'
            else
                echo 'API health check failed'
                docker-compose logs api
            fi

            # Test HTTPS endpoint if available
            if curl -f -k https://localhost/health > /dev/null 2>&1; then
                echo 'HTTPS endpoint is working'
            else
                echo 'HTTPS endpoint check failed (this is normal if SSL is not configured yet)'
            fi
        else
            echo 'API container is not running'
            docker-compose ps
            docker-compose logs
        fi

        # Check nginx status
        if systemctl is-active --quiet nginx; then
            echo 'Nginx is running'
        else
            echo 'Nginx is not running'
        fi

        # Check PostgreSQL status
        if systemctl is-active --quiet postgresql; then
            echo 'PostgreSQL is running'
        else
            echo 'PostgreSQL is not running'
        fi
    "

    log_success "Health check complete"
}

# Main deployment process
main() {
    log_info "Starting QuickSpender deployment..."

    check_config
    test_ssh
    create_remote_dir
    copy_files
    setup_env
    setup_database
    setup_nginx
    setup_ssl
    deploy_app
    check_health

    log_success "Deployment completed successfully!"
    log_info "Your QuickSpender API should be available at: https://$REMOTE_HOST"
    log_info "API direct access: http://$REMOTE_HOST:8080"
    log_info "Database is available at: $REMOTE_HOST:5432"
    log_warning "Don't forget to:"
    log_warning "1. Update the .env file on the remote server with your actual configuration"
    log_warning "2. Update domain name in nginx-site.conf"
    log_warning "3. Run SSL certificate setup: sudo certbot --nginx -d your-domain.com"
    log_warning "4. Update Android app config.xml with your production domain"
}

# Run main function
main "$@"
